import { devices, PlaywrightTestConfig } from "@playwright/test";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface EnvConfig {
  baseUrl: string;
  username: string;
  password: string;
  expectedUserName: string;
}

interface TestConfig extends PlaywrightTestConfig {
  envConfig?: EnvConfig;
}
const environment = process.env.TEST_ENV || "dev";

// ✅ Build path to JSON config
const envConfigPath = path.resolve(__dirname, `config/${environment}.json`);
if (!fs.existsSync(envConfigPath)) {
  throw new Error(
    `Config file for environment "${environment}" not found at ${envConfigPath}`
  );
}

const envConfig: EnvConfig = JSON.parse(
  fs.readFileSync(envConfigPath, "utf-8")
);

const defaultConfig: PlaywrightTestConfig = {
  timeout: 90 * 10000,
  expect: { timeout: 2000 },

  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 0 : 0,

  workers: 2,
  outputDir: "./report/test-results",
  snapshotDir: "./fixture/screenshots/",
  reporter: [
    ["html", { open: "never", outputFolder: "./report/html" }],
    ["junit", { outputFile: "./report/results.xml" }],
    ["allure-playwright", { outputFolder: "./report/allure-results" }],
  ],

  use: {
    headless: false,
    ...devices["Desktop Chrome"],
    deviceScaleFactor: undefined,
    viewport: null,
    launchOptions: {
      args: ["--start-maximized"],
    },
    video: {
      mode: "on",
      size: { width: 1920, height: 1080 },
    },
    trace: "on",
    baseURL: envConfig.baseUrl,
    storageState: "./fixture/.auth/user.json",
  },
  projects: [
    {
      name: "login",
      testMatch: "**/*.setup.ts",
    },
    {
      name: "tests",
      dependencies: ["login"],
      testDir: "./e2e",
      testMatch: ["**/*.spec.ts"],
    },
  ],
};

const config: TestConfig = {
  ...defaultConfig,
  envConfig,
};

export default config;
