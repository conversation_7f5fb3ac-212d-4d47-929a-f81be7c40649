import { Page, Locator, expect } from "@playwright/test";

export class Utilities {
    async logStep(step: string): Promise<void> {
        console.log(`⏭️ Step: ${step}`);
    }
    async logPassStep(step: string): Promise<void> {
        console.log(`✅ Pass: ${step}`);
    }
    async logFailStep(step: string): Promise<void> {
        console.error(`❌ Fail: ${step}`);
    }
    async getSKU(): Promise<string> {
        const prefix = 'PRD';
        const numberPart = Math.floor(1000 + Math.random() * 9000).toString();
        const alphabetPart = Array.from({ length: 3 }, () =>
            String.fromCharCode(65 + Math.floor(Math.random() * 26))
        ).join('');
        const specialChars = '!@#$%^&*()';
        const specialPart = Array.from({ length: 2 }, () =>
            specialChars[Math.floor(Math.random() * specialChars.length)]
        ).join('');
        return prefix + numberPart + alphabetPart + specialPart;
        // return  'PRD' + Math.floor(100000 + Math.random() * 9000)
    }
    async navigateToUrl(page: Page, url: string) {
        await this.logStep("Navigating to Home Page");
        await page.goto(url);
        await page.waitForTimeout(200);
        await page.reload();
        await page.goto(url);
        await page.waitForTimeout(5000);
    }
    async selectDateTwoYearsFromNow(page: any): Promise<void> {
        const today = new Date();
        const targetDate = new Date();
        targetDate.setFullYear(today.getFullYear() + 2);

        const targetYear = targetDate.getFullYear();
        const targetMonth = targetDate.getMonth();
        const targetDay = targetDate.getDate();

        const monthNames = [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ];
        const targetMonthName = monthNames[targetMonth];

        const datePicker = page.locator('lightning-focus-trap');

        const yearSelect = datePicker.locator('lightning-select select[part="select"]');
        await yearSelect.selectOption(String(targetYear));
        this.logStep(`Successfully selected year: ${targetYear}`);

        const currentMonthTextElement = datePicker.locator('h2[id^="month-title-"]');
        const nextMonthButton = datePicker.locator('lightning-button-icon button[title="Next Month"]');
        const previousMonthButton = datePicker.locator('lightning-button-icon button[title="Previous Month"]');

        let currentMonthDisplayedText = await currentMonthTextElement.textContent();
        let currentMonthIndex = monthNames.indexOf(currentMonthDisplayedText as string);

        let monthsToNavigate = (targetMonth - currentMonthIndex + 12) % 12;

        if (monthsToNavigate === 0) {
            this.logStep(`Month is already set to ${targetMonthName}. No navigation needed.`);
        } else if (monthsToNavigate > 6) {
            monthsToNavigate = 12 - monthsToNavigate;
            this.logStep(`Navigating ${monthsToNavigate} months backwards to reach ${targetMonthName}.`);
            for (let i = 0; i < monthsToNavigate; i++) {
                await previousMonthButton.click();
                await page.waitForTimeout(100);
            }
        } else {
            this.logStep(`Navigating ${monthsToNavigate} months forwards to reach ${targetMonthName}.`);
            for (let i = 0; i < monthsToNavigate; i++) {
                await nextMonthButton.click();
                await page.waitForTimeout(100);
            }
        }

        const finalMonthText = await currentMonthTextElement.textContent();
        if (finalMonthText !== targetMonthName) {
            console.warn(`Warning: Expected month to be "${targetMonthName}" but found "${finalMonthText}".`);
        } else {
            this.logStep(`Successfully navigated to month: ${targetMonthName}`);
        }

        const formattedTargetMonth = (targetMonth + 1).toString().padStart(2, '0');
        const formattedTargetDay = targetDay.toString().padStart(2, '0');
        const fullTargetDateValue = `${targetYear}-${formattedTargetMonth}-${formattedTargetDay}`;

        this.logStep(`Attempting to click day with data-value: ${fullTargetDateValue}`);
        const dateCell = datePicker.locator(`td[data-value="${fullTargetDateValue}"]`);
        await dateCell.click();
        this.logStep(`Successfully selected date: ${fullTargetDateValue}`);
    }
    async isElementVisible(page: Page, selectorOrLocator: string | Locator): Promise<boolean> {
        try {
            let locator: Locator;

            // Check if it's already a Locator object
            if (selectorOrLocator && typeof selectorOrLocator === 'object' && 'isVisible' in selectorOrLocator) {
                locator = selectorOrLocator as Locator;
            } else if (typeof selectorOrLocator === 'string') {
                // Handle string selectors
                if (selectorOrLocator.startsWith('/') || selectorOrLocator.startsWith('(')) {
                    // XPath selector
                    locator = page.locator(`xpath=${selectorOrLocator}`);
                } else {
                    // CSS selector
                    locator = page.locator(selectorOrLocator);
                }
            } else {
                console.error('Error: Invalid input. Expected string selector or Locator object. Received:', typeof selectorOrLocator, 'Value:', selectorOrLocator);
                return false;
            }

            const count = await locator.count();
            if (count === 0) return false;
            return await locator.first().isVisible();
        } catch (error) {
            console.error('Error while checking visibility:', error);
            return false;
        }
    }
    async waitForLocator(locator: Locator, timeoutMs: number = 30000, componentRequired?: boolean): Promise<boolean> {
        const intervalMs = 500;
        const startTime = Date.now();
        while (Date.now() - startTime < timeoutMs) {
            try {
                if (await locator.isVisible()) {
                    return true;
                }
            } catch (error) {
                console.log(error);
            }
            await new Promise((resolve) => setTimeout(resolve, intervalMs));
        }
        if (componentRequired) {
            throw new Error(`Timeout: Locator was not visible within ${timeoutMs} ms.\nLocator details: ${locator}`);
        }
        return false;
    }
    async scrollToElement2(page: Page, element: Locator): Promise<void> {
        const timeoutMs = 40000;
        const startTime = Date.now();
        let elementExists = false;
        const viewportSize = {
            width: page.viewportSize()?.width || 1920,
            height: page.viewportSize()?.height || 1080
        };
        let scrollStep = 100;
        while (Date.now() - startTime < timeoutMs) {
            await this.scrollToTopIfAtBottom(page);
            const scrollIntervalMs = 500;
            elementExists = (await element.count()) > 0;
            if (elementExists) {
                console.log("Scrolling slowly to element...");
                await page.evaluate((step: number) => {
                    const scrollingElement =
                        document.scrollingElement || document.body;
                    scrollingElement.scrollBy({ top: step, behavior: "smooth" });
                }, scrollStep);
                const boundingBox = await element.boundingBox();
                if (boundingBox) {
                    const isInViewport =
                        boundingBox.x >= 0 &&
                        boundingBox.y >= 0 &&
                        boundingBox.x + boundingBox.width <= viewportSize!.width &&
                        boundingBox.y + boundingBox.height <= viewportSize!.height;
                    if (isInViewport) {
                        await page.waitForTimeout(1000);
                        await element.waitFor({ state: "visible", timeout: 5000 });
                        return;
                    }
                }
            }
            await page.waitForTimeout(scrollIntervalMs);
        }
        if (!elementExists) {
            throw new Error(`Element ${element.toString()} never appeared in the DOM after ${timeoutMs / 1000} seconds at page ${page.url()}`);
        }
        // Throw an error if the element appeared but did not become visible
        throw new Error(`Failed to make the element visible in the viewport after ${timeoutMs / 1000} seconds`);
    }
    async scrollToTopIfAtBottom(page: Page, scrollIntervalMs: number = 500): Promise<void> {
        // Check if the page is at the bottom
        const isAtBottom = await page.evaluate(() => {
            const scrollingElement = document.scrollingElement || document.body;
            return (
                Math.ceil(
                    scrollingElement.scrollTop + scrollingElement.clientHeight
                ) >= scrollingElement.scrollHeight
            );
        });
        if (isAtBottom) {
            console.log("Page is at the bottom. Scrolling back to the top...");
            await page.evaluate(() => {
                window.scrollTo(0, 0);
            });
            await page.waitForTimeout(scrollIntervalMs);
        }
    }
    async selectFromDropdown(page: any, dropdownElement: any, optionValue: string, clearFirst: boolean = false) {
        await expect(dropdownElement).toBeVisible();
        await dropdownElement.click();
        if (clearFirst) {
            await dropdownElement.selectOption("");
        }
        await dropdownElement.selectOption(optionValue);
        await expect(dropdownElement).toContainText(optionValue);
    }
    async selectFromCustomList(dropdownElement: any, optionText: string, removeExisting: boolean = false, removeButton?: any, customListLocator?: any) {
        // Remove existing selection if needed
        if (removeExisting && removeButton) {
            if (await removeButton.isVisible()) {
                await removeButton.click();
                await expect(removeButton).toBeHidden();
            }
        }
        // Ensure dropdown is ready
        await expect(dropdownElement).toBeEnabled();
        await dropdownElement.click();
        // Use custom list locator if provided, otherwise use default
        const listLocator = customListLocator;
        // Select the option
        const optionToSelect = listLocator.filter({ hasText: optionText });
        await optionToSelect.waitFor({ state: 'visible' });
        await optionToSelect.click();
        // Wait for selection to be reflected
        await expect(dropdownElement).toContainText(optionText);
    }
    async fillCalendarField(page: any, createdDate: string, index: number, dateValue: string, calendarFieldName: string) {
        const calendarField = page.locator(".form-control.input-sm.dateNewClass").nth(index);
        this.scrollToElement2(page, calendarField);
        await page.waitForTimeout(2000);
        // Ensure field is visible and enabled
        await expect(calendarField).toBeVisible({ timeout: 5000 });
        await expect(calendarField).toBeEnabled({ timeout: 5000 });
        // Clear existing value
        await calendarField.click();
        await calendarField.press("Control+A");
        await calendarField.press("Backspace");
        await calendarField.fill(dateValue);
        // Assert value updated
        await expect(calendarField).toHaveValue(dateValue, { timeout: 5000 });
    }
    async selectFromSingleSelect(page: any, dropdownElement: any, optionText: string, customListLocator?: any) {
        await expect(dropdownElement).toBeVisible({ timeout: 5000 });
        await dropdownElement.click();
        const listLocator = customListLocator || page.locator("ul.chosen-results li");
        const match = listLocator.filter({ hasText: optionText }).first();
        await match.waitFor({ state: "visible", timeout: 5000 });
        await match.click();
        // validate selection
        const displayTextLocator = dropdownElement.locator("span");
        await expect(displayTextLocator).toHaveText(optionText, { timeout: 10000 });
    }
    async selectFromSingleSelectv2(page: any,dropdownElement: any,optionText: string,customListLocator?: any) {
        await expect(dropdownElement).toBeVisible({ timeout: 5000 });
        await dropdownElement.click();
        const listLocator = customListLocator || page.locator("ul.chosen-results li");
        // Filter to get matching element that is not hidden
        const match = listLocator.filter({ hasText: optionText }).filter({ has: page.locator(':not([hidden])') }) .first();
        // Wait for at least one visible item in the list (not necessarily the match)
        await expect(listLocator.first()).toBeVisible({ timeout: 5000 });
        // Check visibility of the match
        if (await match.isVisible()) {
            await match.click();
        } else {
            console.warn(`Option "${optionText}" not visible — might already be selected or filtered out.`);
            return;
        }
        // Validate selection
        const displayTextLocator = dropdownElement.locator("span");
        await expect(displayTextLocator).toHaveText(optionText, { timeout: 10000 });
    }
}