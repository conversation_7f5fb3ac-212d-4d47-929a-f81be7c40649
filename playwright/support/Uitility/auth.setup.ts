import path from "path";
import { fileURLToPath } from "url";
import { test as setup } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";
import config from "../../playwright.config.ts";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to save auth state
const authFile = path.join(__dirname, "../../fixture/.auth/user.json");

setup("Authenticate and Save Storage State", async ({ page }) => {
  const app = new AppManager(page);
  const { username, password } = config.envConfig!;
  // Navigate to login
  await page.goto("/login/backoffice");
  // Perform login
  await app.loginInPage.login(username, password);
  // Save cookies, localStorage, and sessionStorage
  await page.context().storageState({ path: authFile });
  app.utilities.logStep(`Storage state saved to ${authFile}`);
});

export { authFile };
