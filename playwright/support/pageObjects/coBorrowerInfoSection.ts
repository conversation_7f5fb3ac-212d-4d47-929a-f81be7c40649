import { expect, Locator, <PERSON> } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";

export class coBorrowerInfoSection{
    private page: Page;
    private coBorrowercard: any;
    private Fname: any;
    private Mname: any;
    private Lname: any;
    private email: any;
    private homeno: any;
    private cellno: any;
    private fax: any;
    private serviceprovider: any;
    private sameAsBorrowerToggle: any;
    private addressField: any;
    private unitField: any;
    private cityField: any;
    private stateDropdown: any;
    private zipCodeField: any;
    private countyDropdown: any;
    private rentOrOwnDropdown: any;
    private coborguaranteeloanyes: any;
    private coborguaranteeloanno: any;
    private coborguaranteeloanNA: any;
    private lengthoftime: any;
    private lessthan2Yes: any;
    private lessthan2No: any;
    private addressFieldbor: any;
    private unitFieldbor: any;
    private cityFieldbor: any;
    private stateDropdownbor: any;
    private zipCodeFieldbor: any;
    private countyDropdownbor: any;
    private rentOrOwnDropdownbor: any;
    private sameAsBorrowerMailing: any;
    private addressFieldbormail: any;
    private unitFieldbormail: any;
    private cityFieldbormail: any;
    private stateDropdownbormail: any;
    private zipCodeFieldbormail: any;
    private countyDropdownbormail: any;
    private placeOfBirth: any;
    private securityno: any;
    private driverlicstate: any;
    private driverlicno: any;
    private unmarried: any;
    private married: any;
    private seperated: any;
    private marriedtoborYes: any;
    private marriedtoborNo: any;
    private uscitizenyes: any;
    private permresiyes: any;
    private nonpermresiyes: any;
    private fornational: any;
    private equifax: any;
    private midfico: any;
    private transunion: any;
    private experian: any;
    private creditrange: any;

    constructor(page: Page) {
        this.page = page;
        this.coBorrowercard = page.locator('.coBorrowerSectionsCard_body')
        this.Fname = page.locator('//*[@id="coBorrowerFName"]');
        this.Mname = page.locator('//*[@id="coBorrowerMName"]');
        this.Lname = page.locator('//*[@id="coBorrowerLName"]');
        this.email = page.locator('//*[@id="coBorrowerEmail"]');
        this.homeno = page.locator('//*[@id="coBPhoneNumber"]');
        this.cellno = page.locator('//*[@id="coBCellNumber"]');
        this.fax = page.locator('//*[@id="coBFax"]');
        this.serviceprovider = page.locator('select#coBServiceProvider');
        this.sameAsBorrowerToggle = page.locator('#iscoborrowerPreAdd ~ span');
        this.addressField = page.locator('//*[@id="coBPresentAddress"]');
        this.unitField = page.locator('//*[@id="coBorrowerUnit"]');
        this.cityField = page.locator('//*[@id="coBPresentCity"]');
        this.stateDropdown = page.locator('select#coBPresentState');
        this.zipCodeField = page.locator('//*[@id="coBPresentZip"]');
        this.countyDropdown = page.locator('select#coBorrowerCounty');
        this.rentOrOwnDropdown = page.locator('select#coBPresentPropType');
        this.coborguaranteeloanyes = page.locator("//label[contains(@for,'cobor_guarantee_radio1')]");
        this.coborguaranteeloanno = page.locator("//label[@for='cobor_guarantee_radio2']");
        this.coborguaranteeloanNA = page.locator("//label[@for='cobor_guarantee_radio3']");
        this.lengthoftime = page.locator('//*[@id="presentPropLengthTimeCoBor"]');
        this.lessthan2Yes = page.locator("//label[contains(@for,'coBResidedPresentAddr_radio1')]");
        this.lessthan2No = page.locator("//label[@for='coBResidedPresentAddr_radio2']");
        this.addressFieldbor = page.locator('//*[@id="coBorPreviousAddress"]');
        this.unitFieldbor = page.locator('//*[@id="coBorPreviousAddress"]');
        this.cityFieldbor = page.locator('//*[@id="coBorPreviousCity"]');
        this.stateDropdownbor = page.locator('select#coBorPreviousState');
        this.zipCodeFieldbor = page.locator('//*[@id="coBorPreviousZip"]');
        this.countyDropdownbor = page.locator('select#coBorrowerPreviousCounty');
        this.rentOrOwnDropdownbor = page.locator('select#coBFormerPropType');
        this.sameAsBorrowerMailing = page.locator('#mailingAddressAsBorrower ~ span');
        this.addressFieldbormail = page.locator('//*[@id="coBorrowerMailingAddress"]');
        this.unitFieldbormail = page.locator('//*[@id="coBorrowerMailingUnit"]');
        this.cityFieldbormail = page.locator('//*[@id="coBorrowerMailingCity"]');
        this.stateDropdownbormail = page.locator('select#coBorrowerMailingState')
        this.zipCodeFieldbormail = page.locator('//*[@id="coBorrowerMailingZip"]');
        this.countyDropdownbormail = page.locator('select#coBorrowerMailingCounty');
        this.placeOfBirth = page.locator('//*[@id="coborrowerPOB"]');
        this.securityno = page.locator('//*[@id="coBSsnNumber"]');
        this.driverlicstate = page.locator('select#coBorDriverLicenseState');
        this.driverlicno = page.locator('//*[@id="coBorDriverLicenseNumber"]');
        this.unmarried = page.locator("//label[contains(@for,'maritalStatusCoBor_1')]");
        this.married = page.locator("//label[@for='maritalStatusCoBor_2']");
        this.seperated = page.locator("//label[@for='maritalStatusCoBor_3']");
        this.marriedtoborYes = page.locator("//label[@for='marriedToBor_1']");
        this.marriedtoborNo = page.locator("//label[@for='marriedToBor_2']");
        this.uscitizenyes = page.locator("//label[@for='coBorrowerCitizenship_1']");
        this.permresiyes = page.locator("//label[@for='coBorrowerCitizenship_2']");
        this.nonpermresiyes = page.locator("//label[@for='coBorrowerCitizenship_3']");
        this.fornational = page.locator("//label[@for='coBorrowerCitizenship_4']");
        this.equifax = page.locator('//*[@id="coBorEquifaxScore"]');
        this.transunion = page.locator('//*[@id="coBorTransunionScore"]');
        this.midfico = page.locator('//*[@id="midFicoScoreCoBor"]');
        this.experian = page.locator('//*[@id="coBorExperianScore"]');
        this.creditrange = page.locator('select#coBorCreditScoreRange');
    }    

    async typeAndValidate(locator: Locator, value: string) {
        await locator.fill(value);
        const actual = await locator.inputValue();
        const digits = actual.replace(/\D/g, '');
        expect(digits).toBe(value);
    }


    async fillAndValidate(locator: Locator, value: string) {
        await locator.fill(value);
        await expect(locator).toHaveValue(value);
    }

    async checkcoBorrowervisibility() {
        //await this.sbaquestionscard.scrollIntoViewIfNeeded();
        await expect(this.coBorrowercard).toBeVisible({ timeout: 5000 });
    }

    async fillFname(name: string){
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.Fname);
        await this.Fname.clear();
        await this.fillAndValidate(this.Fname,name);
    }

    async fillMname(name: string){
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.Mname);
        await this.Mname.clear();
        await this.fillAndValidate(this.Mname,name);
    }

    async fillLname(name: string){
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.Lname);
        await this.Lname.clear();
        await this.fillAndValidate(this.Lname,name);
    }

    async fillemail(email: string){
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.email);
        await this.email.clear();
        await this.fillAndValidate(this.email,email);
    }

    async fillhomeno(no: number){
        await this.homeno.clear();
        await this.typeAndValidate(this.homeno,no.toString());
    }

    async fillcellno(no: number){
        await this.cellno.clear();
        await this.typeAndValidate(this.cellno,no.toString());
    }

    async fillfax(no: number){
        await this.fax.clear();
        await this.typeAndValidate(this.fax,no.toString());
    }

    async selectMobileServiceProvider(provider: string) {
        const dropdown = this.serviceprovider;
        await dropdown.click();
        await dropdown.selectOption({ value: provider }); // e.g., "att"
    }

    async selectSameAsBorrowerAddress(
        expectedState: "on" | "off",
        extraData?: {
            fill?: {address?: string;
            unit?: string;
            city?: string;
            state?: string;
            zip?: string;
            county?: string;
            rentOrOwn?: string;
        }
    }
    ): Promise<void> {
        
        const toggle = this.sameAsBorrowerToggle;

        const isChecked = await toggle.isChecked();

        if (expectedState === "on" && !isChecked) {
            await toggle.check(); // ensures checkbox is ON
        } else if (expectedState === "off" && isChecked) {
            await toggle.uncheck(); // ensures checkbox is OFF
        }

    // wait for UI update
        await this.page.waitForTimeout(300);
        

        if (expectedState === "off" && extraData) {
            await this.addressField.waitFor({ state: "visible" });

            if (extraData.fill?.address) {
                await this.fillAndValidate(this.addressField, extraData.fill.address);
            }
            if (extraData.fill?.unit) {
                await this.fillAndValidate(this.unitField, extraData.fill.unit.toString());
            }
            if (extraData.fill?.city) {
                await this.fillAndValidate(this.cityField, extraData.fill.city);
            }
            if (extraData.fill?.state) {
                await this.stateDropdown.click();
                await this.stateDropdown.selectOption({ value : extraData.fill.state });
            }
            if (extraData.fill?.zip) {
                await this.fillAndValidate(this.zipCodeField, extraData.fill.zip);
            }
            if (extraData.fill?.county) {
                await this.countyDropdown.click();
                await this.countyDropdown.selectOption({ value : extraData.fill.county });
            }
            if (extraData.fill?.rentOrOwn) {
                await this.rentOrOwnDropdown.click();
                await this.rentOrOwnDropdown.selectOption({ value : extraData.fill.rentOrOwn });
            }
        }
    }

    async selectcoborguaranteeloan(
        option: "Yes" | "No" | "NA"
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.coborguaranteeloanyes);

        if (option === "Yes") {
            // Click YES
            await this.coborguaranteeloanyes.click();
        } 
        if(option === "No"){
            await this.coborguaranteeloanno.click();
        }    
            else {
            await this.coborguaranteeloanNA.click();
        }
    }

    async selectlengthoftime(time: number){
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.lengthoftime);

        await this.lengthoftime.clear();
        await this.typeAndValidate(this.lengthoftime,time.toString());
    }

    async selectborresidedlessthan2(
        option: "Yes" | "No",
        extraData?: {
            fill?: {address?: string;
            unit?: string;
            city?: string;
            state?: string;
            zip?: string;
            county?: string;
            rentOrOwn?: string;
        }
    }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.lessthan2Yes);

        if (option === "Yes" ) {
            await this.lessthan2Yes.click();
        } else if (option === "No") {
            await this.lessthan2No.click();
        }

    // wait for UI update
        await this.page.waitForTimeout(300);

        if (option === "Yes" && extraData) {
            await this.addressFieldbor.waitFor({ state: "visible" });

            if (extraData.fill?.address) {
                await this.fillAndValidate(this.addressFieldbor, extraData.fill.address);
            }
            if (extraData.fill?.unit) {
                await this.fillAndValidate(this.unitFieldbor, extraData.fill.unit.toString());
            }
            if (extraData.fill?.city) {
                await this.fillAndValidate(this.cityFieldbor, extraData.fill.city);
            }
            if (extraData.fill?.state) {
                await this.stateDropdownbor.click();
                await this.stateDropdownbor.selectOption({ value : extraData.fill.state });
            }
            if (extraData.fill?.zip) {
                await this.fillAndValidate(this.zipCodeFieldbor, extraData.fill.zip);
            }
            if (extraData.fill?.county) {
                await this.countyDropdownbor.click();
                await this.countyDropdownbor.selectOption({ value : extraData.fill.county });
            }
            if (extraData.fill?.rentOrOwn) {
                await this.rentOrOwnDropdownbor.click();
                await this.rentOrOwnDropdownbor.selectOption({ value : extraData.fill.rentOrOwn });
            }
        }
    }

    async selectMailingAddress(
        expectedState: "on" | "off",
        extraData?: {
            fill?: {address?: string;
            unit?: string;
            city?: string;
            state?: string;
            zip?: string;
            county?: string;
        }
    }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.sameAsBorrowerMailing);

        const toggle = this.sameAsBorrowerMailing;
        const isChecked = await toggle.isChecked();

        if (expectedState === "on" && !isChecked) {
            await toggle.check(); // ensures checkbox is ON
        } else if (expectedState === "off" && isChecked) {
            await toggle.uncheck(); // ensures checkbox is OFF
        }

    // wait for UI update
        await this.page.waitForTimeout(300);
        

        if (expectedState === "off" && extraData) {
            await this.addressFieldbormail.waitFor({ state: "visible" });

            if (extraData.fill?.address) {
                await this.fillAndValidate(this.addressFieldbormail, extraData.fill.address);
            }
            if (extraData.fill?.unit) {
                await this.fillAndValidate(this.unitFieldbormail, extraData.fill.unit.toString());
            }
            if (extraData.fill?.city) {
                await this.fillAndValidate(this.cityFieldbormail, extraData.fill.city);
            }
            if (extraData.fill?.state) {
                await this.stateDropdownbormail.click();
                await this.stateDropdownbormail.selectOption({ value : extraData.fill.state });
            }
            if (extraData.fill?.zip) {
                await this.fillAndValidate(this.zipCodeFieldbormail, extraData.fill.zip);
            }
            if (extraData.fill?.county) {
                await this.countyDropdownbormail.click();
                await this.countyDropdownbormail.selectOption({ value : extraData.fill.county });
            }
        }
    }

    async fillCalendarField(
        dateValue: string,
    ) {
        const app = new AppManager(this.page);
        const calendarField = this.page.locator('//*[@id="coBorrowerDOB"]');
        app.utilities.scrollToElement2(this.page, calendarField);
        await this.page.waitForTimeout(2000);
        // Ensure field is visible and enabled
        await expect(calendarField).toBeVisible({ timeout: 5000 });
        await expect(calendarField).toBeEnabled({ timeout: 5000 });
        // Clear existing value
        await calendarField.click();
        await calendarField.clear();
        await calendarField.fill(dateValue);
        // Assert value updated
        await expect(calendarField).toHaveValue(dateValue, { timeout: 5000 });
    }

    async selectPlaceOfBirth(name: string){
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.placeOfBirth);
        await this.placeOfBirth.clear();
        await this.fillAndValidate(this.placeOfBirth,name);
    }

    async selectsocialsecurityno(no: number){
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.securityno);
        await this.securityno.clear();
        await this.typeAndValidate(this.securityno,no.toString());
    }

    async selectdriverlicsencestate(state: string){
        await this.driverlicstate.click();
        await this.driverlicstate.selectOption({ value : state });
    }

    async selectdriverlicsenceno(no: number){
        await this.driverlicno.clear();
        await this.fillAndValidate(this.driverlicno,no.toString());
    }

    async filldriverlicissuedate(
        dateValue: string,
    ) {
        const calendarField = this.page.locator('//*[@id="coborLicenseIssuance"]');
        await this.page.waitForTimeout(2000);
        // Ensure field is visible and enabled
        await expect(calendarField).toBeVisible({ timeout: 5000 });
        await expect(calendarField).toBeEnabled({ timeout: 5000 });
        // Clear existing value
        await calendarField.click();
        await calendarField.clear();
        await calendarField.fill(dateValue);
        // Assert value updated
        await expect(calendarField).toHaveValue(dateValue, { timeout: 5000 });
    }

    async fillDriverLicenseExp(
        dateValue: string,
    ) {

        const calendarField = this.page.locator('//*[@id="coborLicenseExpiration"]');
        await this.page.waitForTimeout(2000);
        // Ensure field is visible and enabled
        await expect(calendarField).toBeVisible({ timeout: 5000 });
        await expect(calendarField).toBeEnabled({ timeout: 5000 });
        // Clear existing value
        await calendarField.click();
        await calendarField.clear();
        await calendarField.fill(dateValue);
        // Assert value updated
        await expect(calendarField).toHaveValue(dateValue, { timeout: 5000 });
    }

    async selectmaritialstatus(
        option: "Unmarried" | "Married" | "Separated"
    ): Promise<void> {

        if (option === "Unmarried") {
            // Click YES
            await this.unmarried.click();
        } 
        else if(option === "Married"){
            await this.married.click();
        }    
         else {
            await this.seperated.click();
        }
    }

    async selectmarriedtobor(
        option: "Yes" | "No" 
    ): Promise<void> {
        
        if (option === "Yes") {
            // Click YES
            await this.marriedtoborYes.click();
        } 
        else if(option === "No"){
            await this.marriedtoborNo.click();
        }    
    }

    async selectcitizenship(
        option: "US Citizen" | " Perm Resident " | " Non-Perm Resident " | " Foreign National "
    ): Promise<void> {

        if (option === "US Citizen") {
            // Click YES
            await this.uscitizenyes.click();
        } 
        if(option === " Perm Resident "){
            await this.permresiyes.click();
        }    
        if(option === " Non-Perm Resident ") {
            await this.nonpermresiyes.click();
        }
        else if(option === " Foreign National "){
            await this.fornational.click();
        }
    }

    async selectequifaxscore(no: number){
        await this.equifax.clear();
        await this.fillAndValidate(this.equifax,no.toString());
    }

    async selectmidficoscore(no: number){
        await this.midfico.clear();
        await this.fillAndValidate(this.midfico,no.toString());
    }

    async selecttransunionscore(no: number){
        await this.transunion.clear();
        await this.fillAndValidate(this.transunion,no.toString());
    }

    async selectexperianscore(no: number){
        await this.experian.clear();
        await this.fillAndValidate(this.experian,no.toString());
    }

    async selectcreditrange(range: string){
        await this.creditrange.click();
        await this.creditrange.selectOption({ value : range });
    }
}
