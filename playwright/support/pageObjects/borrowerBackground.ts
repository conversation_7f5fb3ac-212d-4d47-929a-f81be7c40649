import { expect, Locator, <PERSON> } from "@playwright/test";
export class borrowerBackground {
  private page: Page;
  private logger: { logStep: (m: string) => void };
  // section containers
  private borOriginAndVisaSection: Locator;
  private borDeclaredBankruptSection: Locator;
  private borOutstandingJudgementsSection: Locator;
  private borActiveLawsuitsSection: Locator;
  private borPropertyTaxLiensSection: Locator;
  private borObligatedInForeclosureSection: Locator;
  private borDelinquentSection: Locator;
  private borOtherFraudCrimesSection: Locator;
  private borDesignatedBeneficiaryAgreementDesc: Locator;
  private previouslyHadShortSaleSection: Locator;
  private anyReturnsAuditedSection: Locator;
  private haveYouDrawnWillSection: Locator;
  private lineOfCreditOrUnusedCreditSection: Locator;
  private substantialInheritancesSection: Locator;
  // citizenship
  private isBorUSCitizenYes: Locator;
  private isBorUSCitizenNo: Locator;
  private borOriginInput: Locator;
  private borVisaStatusInput: Locator;
  // bankruptcy
  private isBorDeclaredBankruptYes: Locator;
  private isBorDeclaredBankruptNo: Locator;
  private personalBankruptcyDropdown: Locator;
  private personalBankruptcyChosen: Locator;
  private bankruptcyTypesMulti: Locator;
  private bankruptcyTypesChosen: Locator;
  private bankruptcyExplanation: Locator;
  // judgments
  private judgementYes: Locator;
  private judgementNo: Locator;
  private judgementExplanation: Locator;
  // lawsuits
  private lawsuitYes: Locator;
  private lawsuitNo: Locator;
  private lawsuitExplanation: Locator;
  // property tax liens
  private taxLienYes: Locator;
  private taxLienNo: Locator;
  private taxLienExplanation: Locator;
  // foreclosure obligation
  private foreclosureYes: Locator;
  private foreclosureNo: Locator;
  private statusForeclosureDropdown: Locator;
  private foreclosureExplanation: Locator;
  // delinquent
  private delinquentYes: Locator;
  private delinquentNo: Locator;
  private delinquentExplanation: Locator;
  // fraud crimes
  private fraudCrimeYes: Locator;
  private fraudCrimeNo: Locator;
  private fraudCrimeExplanation: Locator;
  // designated beneficiary
  private beneficiaryYes: Locator;
  private beneficiaryNo: Locator;
  private beneficiaryExplanation: Locator;
  // pre-foreclosure / foreclosed
  private preForeclosureYes: Locator;
  private preForeclosureNo: Locator;
  private foreclosedYes: Locator;
  private foreclosedNo: Locator;
  // short sale
  private shortSaleYes: Locator;
  private shortSaleNo: Locator;
  private shortSaleDate: Locator;
  private shortSaleExplanation: Locator;
  // background explanation
  private backgroundExplanation: Locator;
  // income tax filed date
  private incomeTaxFiledDate: Locator;
  // returns audited
  private returnsAuditedYes: Locator;
  private returnsAuditedNo: Locator;
  private yearsAuditedInput: Locator;
  // will
  private haveWillYes: Locator;
  private haveWillNo: Locator;
  private executorDetails: Locator;
  // financial plan
  private finPlanYes: Locator;
  private finPlanNo: Locator;
  // credit facilities
  private creditFacilityYes: Locator;
  private creditFacilityNo: Locator;
  private creditFacilityDetails: Locator;
  // inheritances
  private inheritanceYes: Locator;
  private inheritanceNo: Locator;
  private inheritanceDetails: Locator;
  private saveButton: Locator;

  constructor(page: Page, logger?: { logStep: (m: string) => void }) {
    this.page = page;
    this.logger = logger ?? { logStep: (m: string) => console.debug(m) };

    // section containers
    this.borOriginAndVisaSection = page.locator(".borOriginAndVisaTR");
    this.borDeclaredBankruptSection = page.locator(".borDecalredBankruptTR").first();
    this.borOutstandingJudgementsSection = page.locator(".borOutstandingJudgementsTR");
    this.borActiveLawsuitsSection = page.locator(".borActiveLawsuitsTR");
    this.borPropertyTaxLiensSection = page.locator(".borPropertyTaxLiensTR");
    this.borObligatedInForeclosureSection = page.locator(".borObligatedInForeclosureTR").first();
    this.borDelinquentSection = page.locator(".borDelinquentTR");
    this.borOtherFraudCrimesSection = page.locator(".borOtherFraudRelatedCrimesTR");
    this.borDesignatedBeneficiaryAgreementDesc = page.locator(".borDesignatedBeneficiaryAgreementDesc");
    this.previouslyHadShortSaleSection = page.locator(".previouslyHadShortSaleTR").first();
    this.anyReturnsAuditedSection = page.locator(".anyReturnsAuditedTR").first();
    this.haveYouDrawnWillSection = page.locator(".haveYouDrawnWillTR").first();
    this.lineOfCreditOrUnusedCreditSection = page.locator(".lineOfCreditOrUnusedCreditTR").first();
    this.substantialInheritancesSection = page.locator(".substantialInheritancesTR").first();

    // citizenship
    this.isBorUSCitizenYes = page.locator("#isBorUSCitizenYes");
    this.isBorUSCitizenNo = page.locator("#isBorUSCitizenNo");
    this.borOriginInput = page.locator("#borOrigin");
    this.borVisaStatusInput = page.locator("#borVisaStatus");

    // bankruptcy
    this.isBorDeclaredBankruptYes = page.locator("#isBorDecalredBankruptPastYearsYes");
    this.isBorDeclaredBankruptNo = page.locator("#isBorDecalredBankruptPastYearsNo");
    this.personalBankruptcyDropdown = page.locator("#personalBankruptcy");
    this.personalBankruptcyChosen = page.locator("#personalBankruptcy_chosen");
    this.bankruptcyTypesMulti = page.locator("#bankruptcyTypes");
    this.bankruptcyTypesChosen = page.locator("#bankruptcyTypes_chosen");
    this.bankruptcyExplanation = page.locator("#borDecalredBankruptExpln");

    // judgments
    this.judgementYes = page.locator("#isAnyBorOutstandingJudgementsYes");
    this.judgementNo = page.locator("#isAnyBorOutstandingJudgementsNo");
    this.judgementExplanation = page.locator("#borOutstandingJudgementsExpln");

    // lawsuits
    this.lawsuitYes = page.locator("#hasBorAnyActiveLawsuitsYes");
    this.lawsuitNo = page.locator("#hasBorAnyActiveLawsuitsNo");
    this.lawsuitExplanation = page.locator("#borActiveLawsuitsExpln");

    // property tax liens
    this.taxLienYes = page.locator("#hasBorPropertyTaxLiensYes");
    this.taxLienNo = page.locator("#hasBorPropertyTaxLiensNo");
    this.taxLienExplanation = page.locator("#borPropertyTaxLiensExpln");

    // foreclosure
    this.foreclosureYes = page.locator("#hasBorObligatedInForeclosureYes");
    this.foreclosureNo = page.locator("#hasBorObligatedInForeclosureNo");
    this.statusForeclosureDropdown = page.locator("#statusForeclosure");
    this.foreclosureExplanation = page.locator("#borObligatedInForeclosureExpln");

    // delinquent
    this.delinquentYes = page.locator("#isBorPresenltyDelinquentYes");
    this.delinquentNo = page.locator("#isBorPresenltyDelinquentNo");
    this.delinquentExplanation = page.locator("#borDelinquentExpln");

    // fraud crimes
    this.fraudCrimeYes = page.locator("#haveBorOtherFraudRelatedCrimesYes");
    this.fraudCrimeNo = page.locator("#haveBorOtherFraudRelatedCrimesNo");
    this.fraudCrimeExplanation = page.locator("#borOtherFraudRelatedCrimesExpln");

    // designated beneficiary
    this.beneficiaryYes = page.locator("#borDesignatedBeneficiaryAgreementYes");
    this.beneficiaryNo = page.locator("#borDesignatedBeneficiaryAgreementNo");
    this.beneficiaryExplanation = page.locator("#borDesignatedBeneficiaryAgreementExpln");

    // pre-foreclosure / foreclosed
    this.preForeclosureYes = page.locator("#completedPreForecloseYes");
    this.preForeclosureNo = page.locator("#completedPreForecloseNo");
    this.foreclosedYes = page.locator("#hasBorBeenForeclosedYes");
    this.foreclosedNo = page.locator("#hasBorBeenForeclosedNo");

    // short sale
    this.shortSaleYes = page.locator("#previouslyHadShortSaleYes");
    this.shortSaleNo = page.locator("#previouslyHadShortSaleNo");
    this.shortSaleDate = page.locator("#whenWasShortSale");
    this.shortSaleExplanation = page.locator("#explnCircumstancesOfShortSales");

    // background explanation
    this.backgroundExplanation = page.locator("#borBackgroundExplanation");

    // income tax filed date
    this.incomeTaxFiledDate = page.locator("#incomeTaxFiledDate");

    // returns audited
    this.returnsAuditedYes = page.locator("#anyReturnsAuditedYes");
    this.returnsAuditedNo = page.locator("#anyReturnsAuditedNo");
    this.yearsAuditedInput = page.locator("#yearsAudited");

    // will
    this.haveWillYes = page.locator("#haveYouDrawnWillYes");
    this.haveWillNo = page.locator("#haveYouDrawnWillNo");
    this.executorDetails = page.locator("#nameOfExecutorAndYearDrawn");

    // financial plan
    this.finPlanYes = page.locator("#financialPlanPreparedForYouYes");
    this.finPlanNo = page.locator("#financialPlanPreparedForYouNo");

    // credit facilities
    this.creditFacilityYes = page.locator("#lineOfCreditOrUnusedCreditYes");
    this.creditFacilityNo = page.locator("#lineOfCreditOrUnusedCreditNo");
    this.creditFacilityDetails = page.locator("#creditFacilityDetails");

    // inheritances
    this.inheritanceYes = page.locator("#substantialInheritancesYes");
    this.inheritanceNo = page.locator("#substantialInheritancesNo");
    this.inheritanceDetails = page.locator("#substantialInheritancesDetails");
    this.saveButton = page.locator("#saveBtn");
  }

  async clickSave() {
    this.logger.logStep("Clicking Save button");
    await this.saveButton.click();
    // Wait for backend requests finish (AJAX) — networkidle is a generic fallback
    await this.page.waitForLoadState("networkidle");

    // try to wait for common success indicators but ignore if they don't exist
    await this.page
      .locator(".alert-success, .toast-success, .save-success")
      .first()
      .waitFor({ state: "visible", timeout: 3000 })
      .catch(() => { });
    this.logger.logStep("Save clicked and wait complete");
  }
  private toBool(value: boolean | "Yes" | "No"): boolean {
    return value === true || value === "Yes";
  }
  private async clickRadio(yes: Locator, no: Locator, valueIsYes: boolean | "Yes" | "No") {
    const isYes = this.toBool(valueIsYes as any);
    const target = isYes ? yes : no;
    this.logger.logStep(
      `Click radio -> ${isYes ? "Yes" : "No"} (${await target.getAttribute(
        "id"
      )})`
    );
    await target.click({ force: true });
  }
  private async waitVisible(section?: Locator | null, timeout = 5000) {
    if (!section) return;
    await section.waitFor({ state: "visible", timeout }).catch(() => { });
  }
  private async waitHidden(section?: Locator | null, timeout = 3000) {
    if (!section) return;
    try {
      await section.waitFor({ state: "hidden", timeout });
    } catch {
      // already hidden
    }
  }
  private async fillText(locator: Locator, value?: string | number | null) {
    const text = value == null ? "" : String(value);
    this.logger.logStep(`Filling ${await this.safeId(locator)} -> "${text}"`);
    await locator.fill("");
    await locator.fill(text);
  }
  private async safeId(locator: Locator) {
    try {
      return (
        (await locator.getAttribute("id")) ??
        (await locator.getAttribute("name")) ??
        "locator"
      );
    } catch {
      return "locator";
    }
  }
  private async selectByLabelOrValue(selectEl: Locator, labelOrValue: string) {
    try {
      await selectEl.selectOption({ label: labelOrValue });
    } catch {
      await selectEl.selectOption(labelOrValue);
    }
  }
  private async ensureChosenOpen(chosenContainer: Locator) {
    await chosenContainer.click();
    await chosenContainer
      .locator(".chosen-results")
      .waitFor({ state: "visible", timeout: 2000 })
      .catch(() => { });
  }
  private async selectFromChosenSingle(
    chosenContainer: Locator,
    optionText: string
  ) {
    await this.ensureChosenOpen(chosenContainer);
    const item = chosenContainer
      .locator(".chosen-results li", { hasText: optionText })
      .first();
    await item.click();
  }
  private async clearChosenMulti(chosenContainer: Locator) {
    const closeButtons = chosenContainer.locator(".search-choice-close");
    while ((await closeButtons.count()) > 0) {
      await closeButtons.nth(0).click();
    }
  }
  private async setChosenMulti(
    chosenContainer: Locator,
    optionTexts: string[]
  ) {
    // clear existing
    await this.clearChosenMulti(chosenContainer);
    for (const t of optionTexts) {
      await this.ensureChosenOpen(chosenContainer);
      const item = chosenContainer
        .locator(".chosen-results li", { hasText: t })
        .first();
      await item.click();
    }
  }
  private async getSelectedOptionsText(selectEl: Locator): Promise<string[]> {
    return selectEl.evaluate((sel: HTMLSelectElement) =>
      Array.from(sel.selectedOptions).map((o) => o.text)
    );
  }
  private async expectChecked(yes: Locator, no: Locator, isYes: boolean) {
    if (isYes) {
      await expect(yes).toBeChecked();
      await expect(no).not.toBeChecked();
    } else {
      await expect(no).toBeChecked();
      await expect(yes).not.toBeChecked();
    }
  }
  async selectUSCitizen(value: "Yes" | "No") {
    await this.clickRadio(this.isBorUSCitizenYes, this.isBorUSCitizenNo, value);
    if (value === "No") await this.waitVisible(this.borOriginAndVisaSection);
    else await this.waitHidden(this.borOriginAndVisaSection);
  }
  async fillNonCitizenDetails(origin: string, visaStatus: string) {
    await this.waitVisible(this.borOriginAndVisaSection);
    await this.fillText(this.borOriginInput, origin);
    await this.fillText(this.borVisaStatusInput, visaStatus);
  }
  async selectDeclaredBankrupt(value: "Yes" | "No") {
    await this.clickRadio(this.isBorDeclaredBankruptYes, this.isBorDeclaredBankruptNo, value);
    if (value === "Yes")
      await this.waitVisible(this.borDeclaredBankruptSection);
    else await this.waitHidden(this.borDeclaredBankruptSection);
  }
  async selectPersonalBankruptcy(statusLabel: string) {
    if (!statusLabel) return;
    await this.waitVisible(this.borDeclaredBankruptSection);
    await this.selectFromChosenSingle(
      this.personalBankruptcyChosen,
      statusLabel
    );
  }
  async selectBankruptcyTypes(typeLabels: string[]) {
    await this.waitVisible(this.borDeclaredBankruptSection);
    if (!typeLabels || typeLabels.length === 0) {
      await this.clearChosenMulti(this.bankruptcyTypesChosen);
      return;
    }
    await this.setChosenMulti(this.bankruptcyTypesChosen, typeLabels);
  }
  async fillBankruptcyExplanation(text: string) {
    await this.waitVisible(this.borDeclaredBankruptSection);
    await this.fillText(this.bankruptcyExplanation, text);
  }
  async selectOutstandingJudgements(value: "Yes" | "No") {
    await this.clickRadio(this.judgementYes, this.judgementNo, value);
    if (value === "Yes")
      await this.waitVisible(this.borOutstandingJudgementsSection);
    else await this.waitHidden(this.borOutstandingJudgementsSection);
  }
  async fillOutstandingJudgementsExplanation(text: string) {
    await this.waitVisible(this.borOutstandingJudgementsSection);
    await this.fillText(this.judgementExplanation, text);
  }
  async selectActiveLawsuits(value: "Yes" | "No") {
    await this.clickRadio(this.lawsuitYes, this.lawsuitNo, value);
    if (value === "Yes") await this.waitVisible(this.borActiveLawsuitsSection);
    else await this.waitHidden(this.borActiveLawsuitsSection);
  }
  async fillActiveLawsuitsExplanation(text: string) {
    await this.waitVisible(this.borActiveLawsuitsSection);
    await this.fillText(this.lawsuitExplanation, text);
  }
  async selectPropertyTaxLiens(value: "Yes" | "No") {
    await this.clickRadio(this.taxLienYes, this.taxLienNo, value);
    if (value === "Yes")
      await this.waitVisible(this.borPropertyTaxLiensSection);
    else await this.waitHidden(this.borPropertyTaxLiensSection);
  }
  async fillPropertyTaxLiensExplanation(text: string) {
    await this.waitVisible(this.borPropertyTaxLiensSection);
    await this.fillText(this.taxLienExplanation, text);
  }
  async selectObligatedInForeclosure(value: "Yes" | "No") {
    await this.clickRadio(this.foreclosureYes, this.foreclosureNo, value);
    if (value === "Yes")
      await this.waitVisible(this.borObligatedInForeclosureSection);
    else await this.waitHidden(this.borObligatedInForeclosureSection);
  }
  async selectStatusOfForeclosure(statusLabel: string) {
    await this.waitVisible(this.borObligatedInForeclosureSection);
    if (!statusLabel) return;
    await this.selectByLabelOrValue(
      this.statusForeclosureDropdown,
      statusLabel
    );
  }
  async fillForeclosureExplanation(text: string) {
    await this.waitVisible(this.borObligatedInForeclosureSection);
    await this.fillText(this.foreclosureExplanation, text);
  }
  async selectPresentlyDelinquent(value: "Yes" | "No") {
    await this.clickRadio(this.delinquentYes, this.delinquentNo, value);
    if (value === "Yes") await this.waitVisible(this.borDelinquentSection);
    else await this.waitHidden(this.borDelinquentSection);
  }
  async fillDelinquentExplanation(text: string) {
    await this.waitVisible(this.borDelinquentSection);
    await this.fillText(this.delinquentExplanation, text);
  }
  async selectFraudRelatedCrimes(value: "Yes" | "No") {
    await this.clickRadio(this.fraudCrimeYes, this.fraudCrimeNo, value);
    if (value === "Yes")
      await this.waitVisible(this.borOtherFraudCrimesSection);
    else await this.waitHidden(this.borOtherFraudCrimesSection);
  }
  async fillFraudCrimesExplanation(text: string) {
    await this.waitVisible(this.borOtherFraudCrimesSection);
    await this.fillText(this.fraudCrimeExplanation, text);
  }
  async selectDesignatedBeneficiaryAgreement(value: "Yes" | "No") {
    await this.clickRadio(this.beneficiaryYes, this.beneficiaryNo, value);
    if (value === "Yes")
      await this.waitVisible(this.borDesignatedBeneficiaryAgreementDesc);
    else await this.waitHidden(this.borDesignatedBeneficiaryAgreementDesc);
  }
  async fillDesignatedBeneficiaryExplanation(text: string) {
    await this.waitVisible(this.borDesignatedBeneficiaryAgreementDesc);
    await this.fillText(this.beneficiaryExplanation, text);
  }
  async selectCompletedPreForeclose(value: "Yes" | "No") {
    await this.clickRadio(this.preForeclosureYes, this.preForeclosureNo, value);
  }
  async selectForeclosedInLast7Years(value: "Yes" | "No") {
    await this.clickRadio(this.foreclosedYes, this.foreclosedNo, value);
  }
  async selectShortSale(value: "Yes" | "No") {
    await this.clickRadio(this.shortSaleYes, this.shortSaleNo, value);
    if (value === "Yes")
      await this.waitVisible(this.previouslyHadShortSaleSection);
    else await this.waitHidden(this.previouslyHadShortSaleSection);
  }
  async fillShortSaleDate(dateText: string) {
    await this.waitVisible(this.previouslyHadShortSaleSection);
    await this.fillText(this.shortSaleDate, dateText);
  }
  async fillShortSaleExplanation(text: string) {
    await this.waitVisible(this.previouslyHadShortSaleSection);
    await this.fillText(this.shortSaleExplanation, text);
  }
  async fillBackground(text: string) {
    await this.fillText(this.backgroundExplanation, text);
  }
  async fillIncomeTaxFiledDate(dateText: string) {
    await this.fillText(this.incomeTaxFiledDate, dateText);
  }
  async selectAnyReturnsAudited(value: "Yes" | "No") {
    await this.clickRadio(this.returnsAuditedYes, this.returnsAuditedNo, value);
    if (value === "Yes") await this.waitVisible(this.anyReturnsAuditedSection);
    else await this.waitHidden(this.anyReturnsAuditedSection);
  }
  async fillYearsAudited(text: string) {
    await this.waitVisible(this.anyReturnsAuditedSection);
    await this.fillText(this.yearsAuditedInput, text);
  }
  async selectHaveYouDrawnWill(value: "Yes" | "No") {
    await this.page.evaluate((val) => {
      const yesRadio = document.querySelector<HTMLInputElement>(
        "#haveYouDrawnWillYes"
      );
      const noRadio = document.querySelector<HTMLInputElement>(
        "#haveYouDrawnWillNo"
      );
      if (val === "Yes" && yesRadio) {
        yesRadio.checked = true;
        yesRadio.dispatchEvent(new Event("change", { bubbles: true }));
        yesRadio.dispatchEvent(new Event("click", { bubbles: true }));
      } else if (val === "No" && noRadio) {
        noRadio.checked = true;
        noRadio.dispatchEvent(new Event("change", { bubbles: true }));
        noRadio.dispatchEvent(new Event("click", { bubbles: true }));
      }
    }, value);

    if (value === "Yes") {
      await this.waitVisible(this.haveYouDrawnWillSection);
    } else {
      await this.waitHidden(this.haveYouDrawnWillSection);
    }
  }
  async fillExecutorAndYear(text: string) {
    await this.waitVisible(this.haveYouDrawnWillSection);
    await this.fillText(this.executorDetails, text);
  }
  // Financial plan
  async selectFinancialPlanPrepared(value: "Yes" | "No") {
    await this.clickRadio(this.finPlanYes, this.finPlanNo, value);
  }
  // Line of credit / credit facility
  async selectLineOfCreditOrUnusedCredit(value: "Yes" | "No") {
    await this.clickRadio(this.creditFacilityYes, this.creditFacilityNo, value);
    if (value === "Yes")
      await this.waitVisible(this.lineOfCreditOrUnusedCreditSection);
    else await this.waitHidden(this.lineOfCreditOrUnusedCreditSection);
  }
  async fillCreditFacilityDetails(text: string) {
    await this.waitVisible(this.lineOfCreditOrUnusedCreditSection);
    await this.fillText(this.creditFacilityDetails, text);
  }
  async selectSubstantialInheritances(value: "Yes" | "No") {
    await this.clickRadio(this.inheritanceYes, this.inheritanceNo, value);
    if (value === "Yes")
      await this.waitVisible(this.substantialInheritancesSection);
    else await this.waitHidden(this.substantialInheritancesSection);
  }
  async fillInheritanceDetails(text: string) {
    await this.waitVisible(this.substantialInheritancesSection);
    await this.fillText(this.inheritanceDetails, text);
  }
  async validateUSCitizenIs(
    isCitizen: boolean,
    expectedOrigin?: string,
    expectedVisa?: string
  ) {
    await this.expectChecked(
      this.isBorUSCitizenYes,
      this.isBorUSCitizenNo,
      isCitizen
    );
    if (!isCitizen) {
      await this.waitVisible(this.borOriginAndVisaSection);
      if (expectedOrigin)
        await expect(this.borOriginInput).toHaveValue(expectedOrigin);
      if (expectedVisa)
        await expect(this.borVisaStatusInput).toHaveValue(expectedVisa);
    }
  }
  async validateOrigin(origin: string) {
    if (origin) await expect(this.borOriginInput).toHaveValue(origin);
  }
  async validateVisaStatus(visa: string) {
    if (visa) await expect(this.borVisaStatusInput).toHaveValue(visa);
  }
  async validateDeclaredBankruptcyIs(isYes: boolean) {
    await this.expectChecked(this.isBorDeclaredBankruptYes,this.isBorDeclaredBankruptNo,isYes);
  }
  async validatePersonalBankruptcyStatus(label?: string) {
    if (label) {
      await this.waitVisible(this.borDeclaredBankruptSection);
      await expect(
        this.personalBankruptcyChosen.locator("a.chosen-single span")
      ).toHaveText(label);
    }
  }
  async validateBankruptcyTypesContain(expected?: string[]) {
    if (!expected?.length) return; // skip if undefined or empty
    const selected = await this.getSelectedOptionsText(
      this.bankruptcyTypesMulti
    );
    for (const type of expected) {
      if (!selected.includes(type))
        throw new Error(`Expected bankruptcy types to include "${type}"`);
    }
  }
  async validateBankruptcyExplanation(text?: string) {
    if (text) await expect(this.bankruptcyExplanation).toHaveValue(text);
  }
  async validateOutstandingJudgmentsIs(isYes: boolean) {
    await this.expectChecked(this.judgementYes, this.judgementNo, isYes);
  }
  async validateOutstandingJudgmentsExplanation(text?: string) {
    if (text) await expect(this.judgementExplanation).toHaveValue(text);
  }
  async validateActiveLawsuitsIs(isYes: boolean) {
    await this.expectChecked(this.lawsuitYes, this.lawsuitNo, isYes);
  }
  async validateActiveLawsuitsExplanation(text?: string) {
    if (text) await expect(this.lawsuitExplanation).toHaveValue(text);
  }
  async validatePropertyTaxLiensIs(isYes: boolean) {
    await this.expectChecked(this.taxLienYes, this.taxLienNo, isYes);
  }
  async validatePropertyTaxLiensExplanation(text?: string) {
    if (text) await expect(this.taxLienExplanation).toHaveValue(text);
  }
  async validateObligatedInForeclosureIs(isYes: boolean) {
    await this.expectChecked(this.foreclosureYes, this.foreclosureNo, isYes);
  }
  async validateStatusOfForeclosure(label?: string) {
    if (!label) return;

    const value = await this.statusForeclosureDropdown.evaluate(
      (sel: HTMLSelectElement, expectedLabel: string) => {
        const opt = Array.from(sel.options).find(
          (o) => o.text === expectedLabel
        );
        return opt ? opt.value : "";
      },
      label // pass label as argument to evaluate
    );

    await expect(this.statusForeclosureDropdown).toHaveValue(value);
  }
  async validateObligatedInForeclosureExplanation(text?: string) {
    if (text) await expect(this.foreclosureExplanation).toHaveValue(text);
  }
  async validatePresentlyDelinquentIs(isYes: boolean) {
    await this.expectChecked(this.delinquentYes, this.delinquentNo, isYes);
  }
  async validateDelinquentExplanation(text?: string) {
    if (text) await expect(this.delinquentExplanation).toHaveValue(text);
  }
  async validateFraudRelatedCrimesIs(isYes: boolean) {
    await this.expectChecked(this.fraudCrimeYes, this.fraudCrimeNo, isYes);
  }
  async validateFraudCrimesExplanation(text?: string) {
    if (text) await expect(this.fraudCrimeExplanation).toHaveValue(text);
  }
  async validateBeneficiaryAgreementIs(isYes: boolean) {
    await this.expectChecked(this.beneficiaryYes, this.beneficiaryNo, isYes);
  }
  async validateBeneficiaryAgreementExplanation(text?: string) {
    if (text) await expect(this.beneficiaryExplanation).toHaveValue(text);
  }
  async validateCompletedPreForeclosureIs(isYes: boolean) {
    await this.expectChecked(
      this.preForeclosureYes,
      this.preForeclosureNo,
      isYes
    );
  }
  async validateBeenForeclosedIs(isYes: boolean) {
    await this.expectChecked(this.foreclosedYes, this.foreclosedNo, isYes);
  }
  async validateShortSaleIs(isYes: boolean) {
    await this.expectChecked(this.shortSaleYes, this.shortSaleNo, isYes);
  }
  async validateWhenShortSale(dateText?: string) {
    if (dateText) await expect(this.shortSaleDate).toHaveValue(dateText);
  }
  async validateShortSaleExplanation(text?: string) {
    if (text) await expect(this.shortSaleExplanation).toHaveValue(text);
  }
  async validateBackgroundExplanation(text?: string) {
    if (text) await expect(this.backgroundExplanation).toHaveValue(text);
  }
  async validateIncomeTaxFiledDate(dateText?: string) {
    if (dateText) await expect(this.incomeTaxFiledDate).toHaveValue(dateText);
  }
  async validateReturnsAuditedIs(isYes: boolean) {
    await this.expectChecked(
      this.returnsAuditedYes,
      this.returnsAuditedNo,
      isYes
    );
  }
  async validateYearsAudited(text?: string) {
    if (text) await expect(this.yearsAuditedInput).toHaveValue(text);
  }
  async validateHaveWillIs(isYes: boolean) {
    await this.expectChecked(this.haveWillYes, this.haveWillNo, isYes);
  }
  async validateExecutorAndYear(text?: string) {
    if (text) await expect(this.executorDetails).toHaveValue(text);
  }
  async validateFinancialPlanIs(isYes: boolean) {
    await this.expectChecked(this.finPlanYes, this.finPlanNo, isYes);
  }
  async validateCreditFacilityIs(isYes: boolean) {
    await this.expectChecked(
      this.creditFacilityYes,
      this.creditFacilityNo,
      isYes
    );
  }
  async validateCreditFacilityDetails(text?: string) {
    if (text) await expect(this.creditFacilityDetails).toHaveValue(text);
  }
  async validateSubstantialInheritancesIs(isYes: boolean) {
    await this.expectChecked(this.inheritanceYes, this.inheritanceNo, isYes);
  }
  async validateSubstantialInheritancesDetails(text?: string) {
    if (text) await expect(this.inheritanceDetails).toHaveValue(text);
  }
  async fillAll(data: Partial<BorrowerBackgroundData>) {
    this.logger.logStep("borrowerBackground.fillAll() start");

    if (data.isUSCitizen !== undefined) {
      await this.selectUSCitizen(data.isUSCitizen ? "Yes" : "No");
      if (!data.isUSCitizen) {
        await this.fillNonCitizenDetails(
          data.borOrigin ?? "",
          data.borVisaStatus ?? ""
        );
      }
    }

    if (data.declaredBankrupt !== undefined) {
      await this.selectDeclaredBankrupt(data.declaredBankrupt ? "Yes" : "No");
      if (data.declaredBankrupt) {
        if (data.personalBankruptcyStatus)
          await this.selectPersonalBankruptcy(data.personalBankruptcyStatus);
        if (data.bankruptcyTypes)
          await this.selectBankruptcyTypes(data.bankruptcyTypes);
        if (data.bankruptcyExplanation)
          await this.fillBankruptcyExplanation(data.bankruptcyExplanation);
      }
    }

    if (data.outstandingJudgements !== undefined) {
      await this.selectOutstandingJudgements(
        data.outstandingJudgements ? "Yes" : "No"
      );
      if (data.outstandingJudgements && data.outstandingJudgementsExplanation)
        await this.fillOutstandingJudgementsExplanation(
          data.outstandingJudgementsExplanation
        );
    }

    if (data.activeLawsuits !== undefined) {
      await this.selectActiveLawsuits(data.activeLawsuits ? "Yes" : "No");
      if (data.activeLawsuits && data.activeLawsuitsExplanation)
        await this.fillActiveLawsuitsExplanation(
          data.activeLawsuitsExplanation
        );
    }

    if (data.propertyTaxLiens !== undefined) {
      await this.selectPropertyTaxLiens(data.propertyTaxLiens ? "Yes" : "No");
      if (data.propertyTaxLiens && data.propertyTaxLiensExplanation)
        await this.fillPropertyTaxLiensExplanation(
          data.propertyTaxLiensExplanation
        );
    }

    if (data.obligatedInForeclosure !== undefined) {
      await this.selectObligatedInForeclosure(
        data.obligatedInForeclosure ? "Yes" : "No"
      );
      if (data.obligatedInForeclosure) {
        if (data.statusOfForeclosure)
          await this.selectStatusOfForeclosure(data.statusOfForeclosure);
        if (data.foreclosureExplanation)
          await this.fillForeclosureExplanation(data.foreclosureExplanation);
      }
    }

    if (data.presentlyDelinquent !== undefined) {
      await this.selectPresentlyDelinquent(
        data.presentlyDelinquent ? "Yes" : "No"
      );
      if (data.presentlyDelinquent && data.delinquentExplanation)
        await this.fillDelinquentExplanation(data.delinquentExplanation);
    }

    if (data.fraudRelatedCrimes !== undefined) {
      await this.selectFraudRelatedCrimes(
        data.fraudRelatedCrimes ? "Yes" : "No"
      );
      if (data.fraudRelatedCrimes && data.fraudRelatedCrimesExplanation)
        await this.fillFraudCrimesExplanation(
          data.fraudRelatedCrimesExplanation
        );
    }

    if (data.designatedBeneficiaryAgreement !== undefined) {
      await this.selectDesignatedBeneficiaryAgreement(
        data.designatedBeneficiaryAgreement ? "Yes" : "No"
      );
      if (
        data.designatedBeneficiaryAgreement &&
        data.designatedBeneficiaryExplanation
      )
        await this.fillDesignatedBeneficiaryExplanation(
          data.designatedBeneficiaryExplanation
        );
    }

    if (data.completedPreForeclose !== undefined) {
      await this.selectCompletedPreForeclose(
        data.completedPreForeclose ? "Yes" : "No"
      );
    }

    if (data.foreclosedInLast7Years !== undefined) {
      await this.selectForeclosedInLast7Years(
        data.foreclosedInLast7Years ? "Yes" : "No"
      );
    }

    if (data.previousShortSale !== undefined) {
      await this.selectShortSale(data.previousShortSale ? "Yes" : "No");
      if (data.previousShortSale) {
        if (data.whenWasShortSale)
          await this.fillShortSaleDate(data.whenWasShortSale);
        if (data.shortSaleExplanation)
          await this.fillShortSaleExplanation(data.shortSaleExplanation);
      }
    }

    if (data.backgroundExplanation !== undefined)
      await this.fillBackground(data.backgroundExplanation);
    if (data.incomeTaxFiledDate !== undefined)
      await this.fillIncomeTaxFiledDate(data.incomeTaxFiledDate);

    if (data.anyReturnsAudited !== undefined) {
      await this.selectAnyReturnsAudited(data.anyReturnsAudited ? "Yes" : "No");
      if (data.anyReturnsAudited && data.yearsAudited)
        await this.fillYearsAudited(data.yearsAudited);
    }

    if (data.haveYouDrawnWill !== undefined) {
      await this.selectHaveYouDrawnWill(data.haveYouDrawnWill ? "Yes" : "No");
      if (data.haveYouDrawnWill && data.executorAndYear)
        await this.fillExecutorAndYear(data.executorAndYear);
    }

    if (data.financialPlanPrepared !== undefined)
      await this.selectFinancialPlanPrepared(
        data.financialPlanPrepared ? "Yes" : "No"
      );

    if (data.lineOfCreditOrUnusedCredit !== undefined) {
      await this.selectLineOfCreditOrUnusedCredit(
        data.lineOfCreditOrUnusedCredit ? "Yes" : "No"
      );
      if (data.lineOfCreditOrUnusedCredit && data.creditFacilityDetails)
        await this.fillCreditFacilityDetails(data.creditFacilityDetails);
    }

    if (data.substantialInheritances !== undefined) {
      await this.selectSubstantialInheritances(
        data.substantialInheritances ? "Yes" : "No"
      );
      if (data.substantialInheritances && data.substantialInheritancesDetails)
        await this.fillInheritanceDetails(data.substantialInheritancesDetails);
    }

    this.logger.logStep("borrowerBackground.fillAll() finished");
  }
  async validateBorrowerBackgroundSaved(expected: Partial<BorrowerBackgroundData>) {
    this.logger.logStep("Validating borrower background after save - waiting for networkidle");
    await this.page.waitForLoadState("networkidle");
    this.logger.logStep("Validating Citizenship");
    if (expected.isUSCitizen !== undefined) {
      await this.validateUSCitizenIs(
        !!expected.isUSCitizen,
        expected.borOrigin,
        expected.borVisaStatus
      );
    }
    this.logger.logStep("Validating Bankruptcies");
    if (expected.declaredBankrupt !== undefined) {
      await this.validateDeclaredBankruptcyIs(!!expected.declaredBankrupt);
      if (expected.declaredBankrupt) {
        await this.validatePersonalBankruptcyStatus(expected.personalBankruptcyStatus);
        await this.validateBankruptcyTypesContain(expected.bankruptcyTypes);
        await this.validateBankruptcyExplanation(expected.bankruptcyExplanation);
      }
    }
    this.logger.logStep("Validating Outstanding Judgements");
    if (expected.outstandingJudgements !== undefined) {
      await this.validateOutstandingJudgmentsIs(
        !!expected.outstandingJudgements
      );
      await this.validateOutstandingJudgmentsExplanation(
        expected.outstandingJudgementsExplanation
      );
    }
    this.logger.logStep("Validating Active lawsuits");
    if (expected.activeLawsuits !== undefined) {
      await this.validateActiveLawsuitsIs(!!expected.activeLawsuits);
      await this.validateActiveLawsuitsExplanation(
        expected.activeLawsuitsExplanation
      );
    }
    this.logger.logStep("Validating Property tax liens");
    if (expected.propertyTaxLiens !== undefined) {
      await this.validatePropertyTaxLiensIs(!!expected.propertyTaxLiens);
      await this.validatePropertyTaxLiensExplanation(
        expected.propertyTaxLiensExplanation
      );
    }
    this.logger.logStep("Validating Foreclosure obligation");
    if (expected.obligatedInForeclosure !== undefined) {
      await this.validateObligatedInForeclosureIs(
        !!expected.obligatedInForeclosure
      );
      if (expected.obligatedInForeclosure) {
        await this.validateStatusOfForeclosure(expected.statusOfForeclosure);
        await this.validateObligatedInForeclosureExplanation(
          expected.foreclosureExplanation
        );
      }
    }
    this.logger.logStep("Validating Presently delinquent");
    if (expected.presentlyDelinquent !== undefined) {
      await this.validatePresentlyDelinquentIs(!!expected.presentlyDelinquent);
      await this.validateDelinquentExplanation(expected.delinquentExplanation);
    }
    this.logger.logStep("Validating Fraud related crimes");
    if (expected.fraudRelatedCrimes !== undefined) {
      await this.validateFraudRelatedCrimesIs(!!expected.fraudRelatedCrimes);
      await this.validateFraudCrimesExplanation(
        expected.fraudRelatedCrimesExplanation
      );
    }
    this.logger.logStep("Validating Designated beneficiary");
    if (expected.designatedBeneficiaryAgreement !== undefined) {
      await this.validateBeneficiaryAgreementIs(
        !!expected.designatedBeneficiaryAgreement
      );
      await this.validateBeneficiaryAgreementExplanation(
        expected.designatedBeneficiaryExplanation
      );
    }
    this.logger.logStep("Validating Pre-foreclosure & foreclosed");
    if (expected.completedPreForeclose !== undefined)
      await this.validateCompletedPreForeclosureIs(
        !!expected.completedPreForeclose
      );

    if (expected.foreclosedInLast7Years !== undefined)
      await this.validateBeenForeclosedIs(!!expected.foreclosedInLast7Years);
    this.logger.logStep("Validating Short sale");
    if (expected.previousShortSale !== undefined) {
      await this.validateShortSaleIs(!!expected.previousShortSale);
      if (expected.previousShortSale) {
        await this.validateWhenShortSale(expected.whenWasShortSale);
        await this.validateShortSaleExplanation(expected.shortSaleExplanation);
      }
    }
    this.logger.logStep("Validating Background explanation & tax date");
    await this.validateBackgroundExplanation(expected.backgroundExplanation);
    await this.validateIncomeTaxFiledDate(expected.incomeTaxFiledDate);
    this.logger.logStep("Validating Returns audited");
    if (expected.anyReturnsAudited !== undefined) {
      await this.validateReturnsAuditedIs(!!expected.anyReturnsAudited);
      await this.validateYearsAudited(expected.yearsAudited);
    }
    this.logger.logStep("Validating Will");
    if (expected.haveYouDrawnWill !== undefined) {
      await this.validateHaveWillIs(!!expected.haveYouDrawnWill);
      await this.validateExecutorAndYear(expected.executorAndYear);
    }
    this.logger.logStep("Validating Financial plan");
    if (expected.financialPlanPrepared !== undefined)
      await this.validateFinancialPlanIs(!!expected.financialPlanPrepared);
    this.logger.logStep("Validating Credit facility");
    if (expected.lineOfCreditOrUnusedCredit !== undefined) {
      await this.validateCreditFacilityIs(
        !!expected.lineOfCreditOrUnusedCredit
      );
      await this.validateCreditFacilityDetails(expected.creditFacilityDetails);
    }
    this.logger.logStep("Validating Inheritances");
    if (expected.substantialInheritances !== undefined) {
      await this.validateSubstantialInheritancesIs(
        !!expected.substantialInheritances
      );
      await this.validateSubstantialInheritancesDetails(
        expected.substantialInheritancesDetails
      );
    }
    this.logger.logStep("Validation after save completed");
  }
}

// Shared data interface
export interface BorrowerBackgroundData {
  isUSCitizen?: boolean;
  borOrigin?: string;
  borVisaStatus?: string;

  declaredBankrupt?: boolean;
  personalBankruptcyStatus?: string;
  bankruptcyTypes?: string[];
  bankruptcyExplanation?: string;

  outstandingJudgements?: boolean;
  outstandingJudgementsExplanation?: string;

  activeLawsuits?: boolean;
  activeLawsuitsExplanation?: string;

  propertyTaxLiens?: boolean;
  propertyTaxLiensExplanation?: string;

  obligatedInForeclosure?: boolean;
  statusOfForeclosure?: string;
  foreclosureExplanation?: string;

  presentlyDelinquent?: boolean;
  delinquentExplanation?: string;

  fraudRelatedCrimes?: boolean;
  fraudRelatedCrimesExplanation?: string;

  designatedBeneficiaryAgreement?: boolean;
  designatedBeneficiaryExplanation?: string;

  completedPreForeclose?: boolean;
  foreclosedInLast7Years?: boolean;

  previousShortSale?: boolean;
  whenWasShortSale?: string;
  shortSaleExplanation?: string;

  backgroundExplanation?: string;
  incomeTaxFiledDate?: string;

  anyReturnsAudited?: boolean;
  yearsAudited?: string;

  haveYouDrawnWill?: boolean;
  executorAndYear?: string;

  financialPlanPrepared?: boolean;

  lineOfCreditOrUnusedCredit?: boolean;
  creditFacilityDetails?: string;

  substantialInheritances?: boolean;
  substantialInheritancesDetails?: string;
}
