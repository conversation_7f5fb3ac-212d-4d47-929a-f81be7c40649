import { expect, Locator, Page, TestInfo } from "@playwright/test";
import { AppManager } from "../../support/AppManager/PageObjectManager";


export class borrowerInfoTab {
  private page: Page;
  private saveButton: Locator
  
 

  constructor(page: any) {
    this.page = page;
    this.saveButton = page.locator("#saveBtn");
    
    
  }

  async fillAndValidate(locator: Locator, value: string) {
    await locator.fill(value);
    await expect(locator).toHaveValue(value);
  }
  async isSaveButtonDisabled(): Promise<boolean> {
    await this.saveButton.waitFor({ state: "visible" });
    return await this.saveButton.isDisabled();
  }
  
  async openLoanFile(loanFile: string) {
    await this.page.goto(loanFile);
    await expect(this.page).toHaveURL(/supp=VIPhelp/);
    await this.page.waitForLoadState('networkidle');
  }
  async typeAndValidate(locator: Locator, value: string) {
    await locator.type(value);
    await expect(locator).toHaveValue(value, { timeout: 5000 });
  }
  async selectAndValidateRadio(locator: Locator) {
    await locator.click();
    await expect(locator).toBeChecked();
  }
  async selectFromDropdownByValue(
    dropdownElement: any,
    optionValue: string,
    clearFirst: boolean = false
  ) {
    await expect(dropdownElement).toBeVisible();

    if (clearFirst) {
      await dropdownElement.selectOption("");
    }

    await dropdownElement.selectOption({ value: optionValue });

    const actualValue = await dropdownElement.inputValue();
    const actualText = (
      await dropdownElement.locator("option:checked").innerText()
    ).trim();

    // Assert by value
    await expect(actualValue).toBe(optionValue);

    // If you also want to assert by text, uncomment:
    // await expect(actualText).toBe(optionValue);

    await this.page.waitForTimeout(2000); // optional, can be removed
  }
  async selectFromDropdownByLabel(
    dropdownElement: any,
    optionLabel: string,
    clearFirst: boolean = false
  ) {
    await expect(dropdownElement).toBeVisible();

    if (clearFirst) {
      await dropdownElement.selectOption("");
    }

    await dropdownElement.selectOption({ label: optionLabel });

    const actualValue = await dropdownElement.inputValue();
    const actualText = (
      await dropdownElement.locator("option:checked").innerText()
    ).trim();

    await expect(actualText).toBe(optionLabel);

    await this.page.waitForTimeout(2000);
  }
  async selectFromMultiSelect(
    dropdownElement: any,
    optionText: string,
    customListLocator?: any,
    searchInput?: any
  ) {
    await expect(dropdownElement).toBeVisible({ timeout: 5000 });
    await dropdownElement.click();

    const listLocator =
      customListLocator || this.page.locator("ul.chosen-results li");

    if (searchInput) {
      await searchInput.fill("");
      await searchInput.fill(optionText);

      const match = listLocator.filter({ hasText: optionText }).first();
      await match.waitFor({ state: "visible", timeout: 5000 });
      await searchInput.press("Enter");
    } else {
      const match = listLocator.filter({ hasText: optionText }).first();
      await match.waitFor({ state: "visible", timeout: 5000 });
      await match.click();
    }

    // ✅ Robust validation: check the chip/tag inside .search-choice
    const selectedChip = dropdownElement.locator(".search-choice span", {
      hasText: optionText,
    });
    await expect(selectedChip).toBeVisible({ timeout: 10000 });
  }
  async selectFromSingleSelect(
    dropdownElement: any,
    optionText: string,
    customListLocator?: any
  ) {
    await expect(dropdownElement).toBeVisible({ timeout: 5000 });
    await dropdownElement.click();

    const listLocator =
      customListLocator || this.page.locator("ul.chosen-results li");

    const match = listLocator.filter({ hasText: optionText }).first();
    await match.waitFor({ state: "visible", timeout: 5000 });
    await match.click();

    // validate selection
    const displayTextLocator = dropdownElement.locator("span");
    await expect(displayTextLocator).toHaveText(optionText, { timeout: 10000 });
  }
  async clickSaveButton() {
    this.saveButton.click();
  }
  async clickOnSaveButton() {
    await this.saveButton.click();
    await this.page.waitForLoadState('networkidle');
  }
  
}
