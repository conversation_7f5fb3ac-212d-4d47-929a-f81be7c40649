import { expect, Locator, Page } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";
import sharp from "sharp"; // npm install sharp

export class adminInfoSection {
    private page: Page;
    private loanFileCard: Locator;
    private borrowerInfoTab: Locator;
    private selectTheBranchDropdown: Locator;
    private selectTheBrokerPartnerDropdown: Locator;
    private selectTheLoanOfficerDropdown: Locator;
    private fileType: Locator;
    private fileTypeName: Locator;
    private removeAddedFile: Locator;
    private selectTheLoanProgramDropdown: Locator;
    private selectLoanProductDropdown: Locator;
    private selectAdditionalDesiredLoanProgramsDropdown: Locator;
    private desiredProgramList: Locator;
    private deSelectSBALoanPro: Locator;
    private InternalLoanProgram: Locator;
    private internalLoanProgramValue: Locator;
    private removeInternalLoanProgramButton: Locator;
    private leadResourceDropdown: Locator;
    private primaryStatus: Locator;
    private fileSubStatus: Locator;
    private fileSubStatusValueName: Locator;
    private removeSelectedFileSubStatus: Locator;
    private fileSubStatusValue: Locator;
    private toastmessage: Locator;
    private leadResourceInputField: Locator;
    private leadResourceValue: Locator;
    private currentDateForLoan: Locator;
    private closeAndOpenTabs: Locator
    private adminInfoSectionDropdownArrow: Locator;
    private thirdrdPartyInputField: Locator;
    private sbaLoanProduct: Locator;
    private loadingFieldsText: Locator;
    private whereAreYouInTheProcessDropdown: Locator;
    private projectNameInput: Locator;
    private selectTheInternalLoanProgramDropdown: Locator;
    private listValueOfInputFields: Locator;
    private adminInfoCard: Locator;

    private fileTypeInput: Locator;
    private leadResourceDropdownRemoveButton: Locator;
    private removeDesiredProgram: Locator;
    private selectTheInternalLoanProgramDropdownValue: Locator;
    private detailsUpdatedToast: Locator;
    private servicingMemberOptions: Locator;
    private servicingMemberInfoSelected: Locator;

    constructor(page: Page) {
        this.page = page;
        this.adminInfoCard = page.locator("#adminSectionId");
        this.borrowerInfoTab = page.locator("//span[normalize-space()='Borrower Info']");
        this.loanFileCard = page.locator("#kt_page_sticky_card");
        this.selectTheBranchDropdown = page.locator("#branchId");
        this.selectTheBranchDropdown = page.locator("#branchId");
        this.selectTheBrokerPartnerDropdown = page.locator("#agentId");
        this.selectTheLoanOfficerDropdown = page.locator("#secondaryAgentId");
        this.fileType = page.locator('//*[@id="fileModule_chosen"]/ul');
        this.leadResourceInputField = this.page.locator("//input[@placeholder=' - Type Lead Source - ']");
        this.fileTypeName = page.locator("ul.mandatory > li:nth-child(1) > span:nth-child(1)");
        this.fileTypeInput = page.locator("#fileModule_chosen > div:nth-child(2) > ul:nth-child(1)>li");
        this.removeAddedFile = page.locator("ul.mandatory > li:nth-child(1) > a:nth-child(2)");
        this.selectTheLoanProgramDropdown = page.locator("#LMRClientType_chosen");
        this.leadResourceDropdownRemoveButton = page.locator(".leadSource_disp > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > span:nth-child(1)")
        this.sbaLoanProduct = page.locator(".sbaLoanProduct_disp > label:nth-child(1)");
        this.loadingFieldsText = page.locator("text=Loading Fields...");
        this.listValueOfInputFields = page.locator(".chosen-drop .chosen-results li");

        this.whereAreYouInTheProcessDropdown = page.locator("#propDetailsProcess");
        this.selectLoanProductDropdown = page.locator("#sbaLoanProduct_chosen");
        this.selectAdditionalDesiredLoanProgramsDropdown = page.locator("#LMRadditionalLoanProgram_chosen");
        this.selectTheInternalLoanProgramDropdown = page.locator("#LMRInternalLoanProgram_chosen > ul:nth-child(1) > li:nth-child(1) > input:nth-child(1)");
        this.desiredProgramList = page.locator("#LMRadditionalLoanProgram_chosen li");
        this.removeDesiredProgram = page.locator("#LMRadditionalLoanProgram_chosen > ul:nth-child(1) > li:nth-child(1) > a:nth-child(2)");
        this.primaryStatus = page.locator("#primaryStatus");
        this.fileSubStatus = page.locator("#LMRProcessorStatus_chosen > ul:nth-child(1) > li:nth-child(1) > input:nth-child(1)");
        this.fileSubStatusValueName = page.locator("#LMRProcessorStatus_chosen > ul:nth-child(1) > li:nth-child(1) > span:nth-child(1)");
        this.fileSubStatusValue = page.locator("#LMRProcessorStatus_chosen > ul:nth-child(1) > li:nth-child(1) > span:nth-child(1)");
        this.removeSelectedFileSubStatus = page.locator("#LMRProcessorStatus_chosen > ul:nth-child(1) > li:nth-child(1) > a:nth-child(2)");
        this.toastmessage = page.locator(".toast-message");
        this.leadResourceValue = this.page.locator(".autocomplete-w1>div.autocomplete>div:nth-child(1)");
        this.projectNameInput = page.locator("#projectName");
        this.currentDateForLoan = page.locator("#createdDateSpan > h5:nth-child(1)");
        this.closeAndOpenTabs = page.locator("li.nav-item:nth-child(35) > span:nth-child(1)");
        this.adminInfoSectionDropdownArrow = page.locator("#adminSectionId > div:nth-child(1) > div:nth-child(2) > span:nth-child(3)");
        this.thirdrdPartyInputField = page.locator("#_3rdPartyFileId");
        this.removeInternalLoanProgramButton = page.locator("#LMRInternalLoanProgram_chosen > ul:nth-child(1) > li:nth-child(1) > a:nth-child(2)")
        this.internalLoanProgramValue = page.locator("#LMRInternalLoanProgram_chosen > ul:nth-child(1) > li:nth-child(1) > span:nth-child(1)");
        this.InternalLoanProgram = page.locator("//input[@value='Select Internal Loan Program']");
        this.deSelectSBALoanPro = page.locator("#sbaLoanProduct_chosen > a:nth-child(1) > abbr:nth-child(2)");
        this.leadResourceDropdown = this.page.locator("//input[@id='leadSource']");
        this.servicingMemberOptions = page.locator("#servicingMemberInfo_chosen .chosen-results li");
        this.servicingMemberInfoSelected = page.locator("#servicingMemberInfo_chosen .chosen-choices li.search-choice");
    }

    async selectFromCustomList(
        dropdownElement: any,
        optionText: string,
        removeExisting: boolean = false,
        removeButton?: any,
        customListLocator?: any
    ) {
        // Remove existing selection if needed
        if (removeExisting && removeButton) {
            if (await removeButton.isVisible()) {
                await removeButton.click();
                await expect(removeButton).toBeHidden({ timeout: 5000 });
            }
        }

        // Ensure dropdown is ready
        await expect(dropdownElement).toBeEnabled({ timeout: 5000 });
        await dropdownElement.click();
        // Use custom list locator if provided, otherwise use default
        const listLocator = customListLocator || this.listValueOfInputFields;
        // Select the option
        const optionToSelect = listLocator.filter({ hasText: optionText });
        await optionToSelect.waitFor({ state: "visible", timeout: 5000 });
        await optionToSelect.click();
        // Wait for selection to be reflected
        await expect(dropdownElement).toContainText(optionText, { timeout: 15000 });
        await this.page.waitForTimeout(2000);
    }
    async checkAdminInfoHeadingVisibility() {
        await expect(this.adminInfoCard).toBeVisible();
    }
    async takeScreenShotOfAdminInfoSectionAndValidate(
        name: string
    ): Promise<void> {
        await expect(this.adminInfoCard).toBeVisible();

        // Hide elements before screenshot
        await this.page.evaluate(() => {
            const selectors = [
                "#kt_header",
                "#kt_brand",
                "#kt_aside_menu_wrapper",
                "#kt_subheader",
                "div.card-header:nth-child(2)",
                "div.card-header:nth-child(3)",
            ];
            selectors.forEach((sel) => {
                const el = document.querySelector(sel) as HTMLElement;
                if (el) el.style.visibility = "hidden";
            });
        });

        console.log(`Validating element screenshot for ${name}`);
        await expect(this.adminInfoCard).toHaveScreenshot(`${name}.png`, {
            maxDiffPixelRatio: 0.01,
            timeout: 30000,
        });

        await this.page.waitForTimeout(5000);

        // Re-show elements
        await this.page.evaluate(() => {
            const selectors = [
                "#kt_header",
                "#kt_brand",
                "#kt_aside_menu_wrapper",
                "#kt_subheader",
                "div.card-header:nth-child(2)",
                "div.card-header:nth-child(3)",
            ];
            selectors.forEach((sel) => {
                const el = document.querySelector(sel) as HTMLElement;
                if (el) el.style.visibility = "visible";
            });
        });

        await this.page.waitForTimeout(5000);
    }
    async takeScreenShotOfLoanFileAndValidate(name: string): Promise<void> {
        const app = new AppManager(this.page);
        await expect(this.adminInfoCard).toBeVisible();
        await this.page.addStyleTag({
            content: `#kt_header  { visibility: hidden !important; }`,
        });
        await this.page.addStyleTag({
            content: `#kt_brand  { visibility: hidden !important; }`,
        });
        await this.page.addStyleTag({
            content: `#kt_aside_menu_wrapper  { visibility: hidden !important; }`,
        });
        await this.page.addStyleTag({
            content: `#kt_subheader  { visibility: hidden !important; }`,
        });
        await this.page.addStyleTag({
            content: `div.card-header:nth-child(2)  { visibility: hidden !important; }`,
        });
        await this.page.addStyleTag({
            content: `div.card-header:nth-child(3)  { visibility: hidden !important; }`,
        });
        console.log(`Validating element screenshot for ${name}`);
        await expect(this.loanFileCard).toHaveScreenshot(`${name}.png`, {
            maxDiffPixelRatio: 0.01,
            timeout: 30000,
        });
    }
    async checkLoanFileCardVisibility() {
        await expect(this.loanFileCard).toBeVisible();
    }
    async clickOnBorrowerInfoTab() {
        await this.borrowerInfoTab.click();
    }
    async takeFullElementScreenshot(name: string) {
        const elementHandle = await this.loanFileCard.elementHandle();
        if (!elementHandle) throw new Error("Element not found");

        // Get element size
        const box = await elementHandle.boundingBox();
        if (!box) throw new Error("Bounding box not found");

        const viewportHeight = this.page.viewportSize()?.height || 800;
        const totalHeight = await this.page.evaluate(
            (el) => el.scrollHeight,
            elementHandle
        );

        let offset = 0;
        let screenshots: Buffer[] = [];

        while (offset < totalHeight) {
            await this.page.evaluate(
                ({ el, y }) => {
                    el.scrollTop = y;
                },
                { el: elementHandle, y: offset }
            );

            await this.page.waitForTimeout(300); // let UI render

            const shot = await elementHandle.screenshot({
                //@ts-ignore
                clip: {
                    x: box.x,
                    y: box.y,
                    width: box.width,
                    height: Math.min(viewportHeight, totalHeight - offset),
                },
            });
            screenshots.push(shot);

            offset += viewportHeight;
        }

        // Create blank canvas for stitched image
        let stitched = sharp({
            create: {
                width: Math.floor(box.width),
                height: Math.floor(totalHeight),
                channels: 4,
                background: { r: 255, g: 255, b: 255, alpha: 1 },
            },
        });

        let yOffset = 0;
        for (const img of screenshots) {
            const metadata = await sharp(img).metadata();

            // Resize to element width (prevents "larger image" error)
            const resized = await sharp(img)
                .resize({ width: Math.floor(box.width) })
                .toBuffer();

            stitched = stitched.composite([
                { input: resized, top: yOffset, left: 0 },
            ]);

            yOffset += metadata.height || 0;
        }

        await stitched.toFile(`${name}.png`);
    }
    async selectFromDropdown(
        dropdownElement: any,
        optionValue: string,
        clearFirst: boolean = false
    ) {
        await expect(dropdownElement).toBeVisible();
        await dropdownElement.click();
        if (clearFirst) {
            await dropdownElement.selectOption("");
        }
        await dropdownElement.selectOption(optionValue);
        await expect(dropdownElement).toContainText(optionValue);
        await this.page.waitForTimeout(2000);
    }
    async selectBranch(branchName: string) {
        await this.selectFromDropdown(this.selectTheBranchDropdown, branchName);
    }
    async selectBrokerPartner(brokerName: string) {
        await this.selectFromDropdown(
            this.selectTheBrokerPartnerDropdown,
            brokerName
        );
    }
    async selectLoanOfficer(officerName: string) {
        await this.selectFromDropdown(this.selectTheLoanOfficerDropdown, officerName);
        await this.page.waitForLoadState("networkidle");
    }
    async addFileType(fileType: string) {
        // Remove existing file type if present
        if (await this.removeAddedFile.isVisible()) {
            await this.removeAddedFile.click();
            // Wait until label is gone
            await expect(this.sbaLoanProduct).toBeHidden({ timeout: 10000 });
        }
        // Wait for "Loading Fields..." cycle to finish
        const loadingText = this.loadingFieldsText;
        await loadingText
            .waitFor({ state: "hidden", timeout: 15000 })
            .catch(() => { });
        // Ensure dropdown is ready
        await expect(this.fileType).toBeVisible({ timeout: 5000 });
        // Select new file type
        await this.fileType.click();
        const optionToSelect = this.listValueOfInputFields.filter({
            hasText: fileType,
        });
        await optionToSelect.waitFor({ state: "visible", timeout: 5000 });
        await optionToSelect.click();
        // Wait for label to appear — meaning file type applied in UI
        await expect(this.sbaLoanProduct).toBeVisible({ timeout: 15000 });
    }
    async selectLoanProgram(programType: string) {
        await this.selectFromCustomList(
            this.selectTheLoanProgramDropdown,
            programType
        );
    }
    async SBALoanproduct(productType: string) {
        await this.selectFromCustomList(this.selectLoanProductDropdown, productType);
    }
    async validateAllDetailsAddedToInputFieldForAdminInfoSection(
        branchName: string,
        brokerName: string,
        loanOfficerName: string,
        fileTypeName: string,
        loanProgramName: string,
        sbaLoanProductName: string,
        additionalDesiredLoanProgramsName: string,
        internalLoanProgramName: string,
        whereAreYouInTheProcessName: string,
        primaryClientFileStatusName: string,
        fileSubStatusName: string,
        projectNameValue: string,
        receivedDate: string,
        borrowerCallbackDate: string,
        welcomeCallDate: string,
        actualClosingDate: string,
        targetClosingDate: string,
        hearingDate: string,
        thirdrdPartyId: string,
        loanDocumentDate: string,
        disclosureSentDate: string
    ) {
        const app = new AppManager(this.page);
        await this.page.waitForLoadState('networkidle');
        await expect(this.selectTheBranchDropdown).toContainText(branchName);
        await expect(this.selectTheBrokerPartnerDropdown).toContainText(brokerName);
        await expect(this.selectTheLoanOfficerDropdown).toContainText(loanOfficerName);
        // [this is need to be fix]
        let locator = this.page.locator("#LMRInternalLoanProgram_chosen > ul:nth-child(1) > li:nth-child(1) > span:nth-child(1)")
        await expect(locator).toContainText(internalLoanProgramName);
        // Validate file type (check if it's displayed in the chosen dropdown)
        await expect(this.fileTypeName).toContainText(fileTypeName);

        // Validate process and status fields
        await expect(this.whereAreYouInTheProcessDropdown).toContainText(whereAreYouInTheProcessName);

        app.utilities.logStep("Validating the loan program");
        await expect(this.selectTheLoanProgramDropdown).toContainText(loanProgramName);
        app.utilities.logStep("Validating the sba loan product");
        await expect(this.selectLoanProductDropdown).toContainText(sbaLoanProductName);
        app.utilities.logStep("Validating the desired loan product");
        await expect(this.selectAdditionalDesiredLoanProgramsDropdown).toContainText(additionalDesiredLoanProgramsName);
        app.utilities.logStep("Validating the internal loan program");
        await expect(this.internalLoanProgramValue).toContainText(internalLoanProgramName);
        app.utilities.logStep("Validating the where are you in the process name");
        await expect(this.whereAreYouInTheProcessDropdown).toContainText(whereAreYouInTheProcessName);
        app.utilities.logStep("Validating the primary client file status name");
        await expect(this.primaryStatus).toContainText(primaryClientFileStatusName);
        app.utilities.logStep("Validating the file sub status name");
        await expect(this.fileSubStatusValueName).toContainText(fileSubStatusName);
        app.utilities.logStep("Validating the project name value");
        await expect(this.projectNameInput).toHaveValue(projectNameValue);
        app.utilities.logStep("Validating the thirdrd party id [pending validation]");
        // await expect(this.thirdrdPartyInputField).toContainText(thirdrdPartyId);

        const dateFields = await this.page.locator(".form-control.input-sm.dateNewClass").all();

        // Map of expected dates by index (adjust indices based on your form layout)
        const dateValidations = [
            { index: 0, value: receivedDate, fieldName: "Received Date" },
            { index: 1, value: borrowerCallbackDate, fieldName: "Borrower Callback Date", },
            { index: 2, value: welcomeCallDate, fieldName: "Welcome Call Date" },
            { index: 3, value: actualClosingDate, fieldName: "Actual Closing Date" },
            { index: 4, value: targetClosingDate, fieldName: "Target Closing Date" },
            { index: 5, value: hearingDate, fieldName: "Hearing Date" },
            { index: 6, value: disclosureSentDate, fieldName: "Disclosure Sent Date", },
            { index: 7, value: loanDocumentDate, fieldName: "Loan Document Date" },
        ];

        // Validate each date field
        for (const dateValidation of dateValidations) {
            if (dateFields[dateValidation.index]) {
                const actualValue = await dateFields[dateValidation.index].inputValue();
                expect(actualValue).toBe(dateValidation.value);
            }
        }
        console.log("All admin info section details validated successfully");
    }
    async validateAllRemovedDetailsAddedToInputFieldForAdminInfoSection(
        branchName: string,
        brokerName: string,
        loanOfficerName: string,
        fileTypeName: string,
        loanProgramName: string,
        sbaLoanProductName: string,
        additionalDesiredLoanProgramsName: string,
        internalLoanProgramName: string,
        whereAreYouInTheProcessName: string,
        primaryClientFileStatusName: string,
        fileSubStatusName: string,
        projectNameValue: string,
        receivedDate: string,
        borrowerCallbackDate: string,
        welcomeCallDate: string,
        actualClosingDate: string,
        targetClosingDate: string,
        hearingDate: string,
        thirdrdPartyId: string,
        loanDocumentDate: string,
        disclosureSentDate: string
    ) {
        const app = new AppManager(this.page)
        // Validate dropdown selections
        await expect(this.selectTheBranchDropdown).toContainText(branchName);
        await expect(this.selectTheBrokerPartnerDropdown).toContainText(brokerName);
        await expect(this.selectTheLoanOfficerDropdown).toContainText(loanOfficerName);

        // Validate file type (check if it's displayed in the chosen dropdown)
        await expect(this.fileTypeName).toContainText(fileTypeName);

        // Validate loan programs
        await expect(this.selectTheLoanProgramDropdown).toContainText(loanProgramName);
        await expect(this.selectLoanProductDropdown).toContainText(sbaLoanProductName);
        await expect(this.selectAdditionalDesiredLoanProgramsDropdown).toContainText(additionalDesiredLoanProgramsName);

        await expect(this.InternalLoanProgram).toContainText(internalLoanProgramName);

        // Validate process and status fields
        await expect(this.whereAreYouInTheProcessDropdown).toContainText(whereAreYouInTheProcessName);
        await app.utilities.scrollToElement2(this.page, this.primaryStatus);
        await expect(this.primaryStatus).toContainText(primaryClientFileStatusName);
        await expect(this.fileSubStatus).toContainText(fileSubStatusName);

        // Validate project name
        await app.utilities.scrollToElement2(this.page, this.projectNameInput);
        await expect(this.projectNameInput).toHaveValue(projectNameValue);

        // Validate all date fields
        // Get all date input fields
        const dateFields = await this.page.locator('.adminSectionCard_body .form-control.input-sm.dateNewClass').all();
        await app.utilities.scrollToElement2(this.page, this.leadResourceInputField);
        await app.utilities.scrollToElement2(this.page, this.currentDateForLoan);
        // Map of expected dates by index (adjust indices based on your form layout)
        const dateValidations = [
            { index: 0, value: receivedDate, fieldName: "Received Date" },
            { index: 1, value: borrowerCallbackDate, fieldName: "Borrower Callback Date" },
            { index: 2, value: welcomeCallDate, fieldName: "Welcome Call Date" },
            { index: 3, value: actualClosingDate, fieldName: "Actual Closing Date" },
            { index: 4, value: targetClosingDate, fieldName: "Target Closing Date" },
            { index: 5, value: hearingDate, fieldName: "Hearing Date" },
            { index: 6, value: disclosureSentDate, fieldName: "Disclosure Sent Date" },
            { index: 7, value: loanDocumentDate, fieldName: "Loan Document Date" },
        ];

        // Validate each date field
        for (const dateValidation of dateValidations) {
            if (dateFields[dateValidation.index]) {
                const actualValue = await dateFields[dateValidation.index].inputValue();
                expect(actualValue).toBe(dateValidation.value);
            }
        }
        await app.utilities.scrollToElement2(this.page, this.thirdrdPartyInputField)
        // await expect(this.thirdrdPartyInputField).toHaveText(thirdrdPartyId)
        console.log('All admin info section details validated successfully after removed');
    }
    async selectAdditionalDesiredLoanPrograms(desiredProgram: string) {
        const app = new AppManager(this.page);
        let isVisible = await app.utilities.isElementVisible(this.page, "#LMRadditionalLoanProgram_chosen > ul:nth-child(1) > li:nth-child(1) > a:nth-child(2)");
        if (isVisible) {
            this.page.locator("#LMRadditionalLoanProgram_chosen > ul:nth-child(1) > li:nth-child(1) > a:nth-child(2)").click();
            // Note: Using desiredProgramList as the custom list locator for this specific dropdown
            await this.selectFromCustomList(this.selectAdditionalDesiredLoanProgramsDropdown, desiredProgram, false, null, this.desiredProgramList);
            await this.page.waitForLoadState("domcontentloaded");
        } else {
            // Note: Using desiredProgramList as the custom list locator for this specific dropdown
            await this.selectFromCustomList(this.selectAdditionalDesiredLoanProgramsDropdown, desiredProgram, false, null, this.desiredProgramList);
            await this.page.waitForLoadState("domcontentloaded");
        }
    }
    async selectPrimaryClientFileStatus(statusName: string) {
        await this.selectFromDropdown(this.primaryStatus, statusName, false);
        await this.page.waitForLoadState("networkidle");
    }
    async selectFileSubStatus(statusName: string) {
        const app = new AppManager(this.page);
        let visible = await app.utilities.isElementVisible(this.page, this.removeSelectedFileSubStatus);
        if (visible) {
            // This method has special handling for toast message, so keeping custom implementation
            await this.removeSelectedFileSubStatus.click();
            await this.page.waitForLoadState("networkidle");
            await this.fileSubStatus.click();
            const optionToSelect = this.listValueOfInputFields.filter({ hasText: statusName, });
            await optionToSelect.waitFor({ state: "visible", timeout: 5000 });
            await optionToSelect.click();
            await this.page.waitForLoadState("networkidle");
            await expect(this.toastmessage).toHaveText('Please click on the "Save" button to check and trigger any related automation(s).');
            await expect(this.fileSubStatusValue).toContainText(statusName, { timeout: 15000, });
            await this.page.waitForLoadState("networkidle");
        } else {
            await this.fileSubStatus.click();
            await this.page.waitForLoadState("networkidle");
            const optionToSelect = this.listValueOfInputFields.filter({ hasText: statusName, });
            await optionToSelect.waitFor({ state: "visible", timeout: 5000 });
            await optionToSelect.click();
            await this.page.waitForLoadState("networkidle");
            await expect(this.toastmessage).toHaveText('Please click on the "Save" button to check and trigger any related automation(s).');
            await this.page.waitForLoadState("networkidle");
            await expect(this.fileSubStatusValue).toContainText(statusName, { timeout: 15000, });
            await this.page.waitForLoadState("networkidle");
        }
    }
    async selectLeadResource() {
        // This uses autocomplete pattern, not standard dropdown
        await this.leadResourceDropdown.click();
        await this.leadResourceValue.click();
        await this.page.waitForLoadState("networkidle");
        await expect(this.leadResourceDropdown).toHaveValue("Lead Source");
        await this.page.waitForLoadState("networkidle");
    }
    async deSelectFileSubStatus() {
        await this.page.locator("#LMRProcessorStatus_chosen > ul:nth-child(1) > li:nth-child(1) > a:nth-child(2)").click();
        await this.page.waitForLoadState("networkidle");
    }
    async deSelectLeadSource() {
        await this.page.locator(".leadSource_disp > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > span:nth-child(1)").click();
        await this.page.waitForLoadState("networkidle");
    }
    async fillProjectName(projectName: string) {
        await expect(this.projectNameInput).toBeVisible();
        await this.projectNameInput.fill(projectName);
        await expect(this.projectNameInput).toHaveValue(projectName, {
            timeout: 5000,
        });
    }
    async getCreatedDateOfLoanFile() {
        await this.currentDateForLoan.waitFor({ state: "visible", timeout: 5000 });
        const createdDateText = await this.currentDateForLoan.textContent();
        return createdDateText ? createdDateText.trim() : "";
    }
    async fillCalendarField(createdDate: string, index: number, dateValue: string, calendarFieldName: string) {
        const app = new AppManager(this.page);
        const calendarField = await this.page.locator(".form-control.input-sm.dateNewClass").nth(index);
        await app.utilities.scrollToElement2(this.page, calendarField);
        await this.page.waitForTimeout(2000);
        // Ensure field is visible and enabled
        await expect(calendarField).toBeVisible({ timeout: 5000 });
        await expect(calendarField).toBeEnabled({ timeout: 5000 });
        // Clear existing value
        await calendarField.click();
        await calendarField.press("Control+A");
        await calendarField.press("Backspace");
        await calendarField.fill(dateValue);
        // Assert value updated
        await expect(calendarField).toHaveValue(dateValue, { timeout: 5000 });
    }
    async add3rdPartyFileId(id: string) {
        await expect(this.thirdrdPartyInputField).toBeVisible();
        await this.thirdrdPartyInputField.fill(id);
    }
    async takeScreenShot(name: string): Promise<void> {
        const screenshotPath = `./fixture/screenshots/${name}.png`;
        await this.page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(`Screenshot taken and saved to ${screenshotPath}`);
    }
    async validateScreenShot(name: string): Promise<void> {
        console.log(`Validate the screen shot for ${name}`);
        await expect(this.page).toHaveScreenshot(`${name}.png`, {
            fullPage: true,
            timeout: 50000,
        });
    }
    async clickOncloseAllOpenTabsButton() {
        await this.closeAndOpenTabs.click();
    }
    async openAdminInfoSection() {
        await this.adminInfoSectionDropdownArrow.click();
    }
    async deSelectCalenderfeild(index: number, calendarFieldName: string) {
        const app = new AppManager(this.page);
        const calendarField = await this.page.locator('.form-control.input-sm.dateNewClass').nth(index);
        await app.utilities.scrollToElement2(this.page, calendarField);
        await calendarField.click();
        await calendarField.press('Control+A');
        await calendarField.press('Backspace');
    }
    async deSelectBrokerReferringParty() {
        const deSelect = this.page.locator("#referringParty");
        await deSelect.click();
        await deSelect.press('Control+A');
        await deSelect.press('Backspace');
        await this.page.waitForLoadState("networkidle");
    }
    async DeSelectSBALoanproduct() {
        await this.deSelectSBALoanPro.click();
        await this.page.waitForLoadState("networkidle");
    }
    async DeselectAdditionalDesiredLoanPrograms() {
        await this.page.locator("(//a[@class='search-choice-close'])[2]").click();
        await this.page.waitForLoadState('domcontentloaded');
    }
    async deSelectInternalLoanProgram() {
        const app = new AppManager(this.page);
        await app.utilities.scrollToElement2(this.page, this.removeInternalLoanProgramButton)
        await this.removeInternalLoanProgramButton.click();
        await this.page.waitForLoadState("networkidle");
    }
    async selectWhereAreYouInTheProcess(whereAreYouInTheProcessName: string) {
        await this.selectFromDropdown(this.whereAreYouInTheProcessDropdown, whereAreYouInTheProcessName, true);
        await this.page.waitForLoadState("networkidle");
    }
    async selectFromCustomListAndValidateSelectedOption(
        dropdownElement: any,
        optionText: string,
        removeExisting: boolean = false,
        removeButton?: any,
        customListLocator?: any,
        spanValue?: any
    ) {
        // Remove existing selection if needed
        if (removeExisting && removeButton) {
            if (await removeButton.isVisible()) {
                await removeButton.click();
                await expect(removeButton).toBeHidden({ timeout: 5000 });
            }
        }

        // Ensure dropdown is ready
        await expect(dropdownElement).toBeEnabled({ timeout: 5000 });
        await dropdownElement.click();
        // Use custom list locator if provided, otherwise use default
        const listLocator = customListLocator || this.listValueOfInputFields;
        // Select the option
        const optionToSelect = listLocator.filter({ hasText: optionText });
        await optionToSelect.waitFor({ state: "visible", timeout: 5000 });
        await optionToSelect.click();
        await this.page.waitForLoadState("networkidle");
        // Wait for selection to be reflected
        await expect(spanValue).toContainText(optionText, { timeout: 15000 });
        await this.page.waitForLoadState("networkidle");
    }
    async selectInternalLoanProgram(programType: string) {
        const app = new AppManager(this.page);
        await app.utilities.scrollToElement2(this.page, this.selectTheInternalLoanProgramDropdown);

        await expect(this.selectTheInternalLoanProgramDropdown).toBeEnabled({ timeout: 5000, });
        await this.selectTheInternalLoanProgramDropdown.click();

        await this.selectFromCustomListAndValidateSelectedOption(this.selectTheInternalLoanProgramDropdown, programType, false, null, null, this.internalLoanProgramValue);
        await this.page.waitForLoadState("networkidle");
    }
}
