import { expect, Locator, <PERSON> } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";

export class loginPage {
  private page: Page;

  // Locators
  private loginForm: Locator;
  private loginHeader: Locator;
  private usernameField: Locator;
  private passwordField: Locator;
  private forgotPasswordLink: Locator;
  private loginButton: Locator;
  private updateBillingLink: Locator;
  private errorUserName: Locator;
  private errorPassword: Locator;
  private invalidAccountError: Locator;
  private subscriptionHeader: Locator;
  private landingWiseLogo: Locator;

  constructor(page: Page) {
    this.page = page;
    this.loginForm = page.locator("#LoginFormDiv");
    this.loginHeader = page.locator(
      "//h3[normalize-space()='Back Office User']"
    );
    this.usernameField = page.locator("#userName");
    this.passwordField = page.locator("#pwd");
    this.forgotPasswordLink = page.locator("#kt_login_forgot");
    this.loginButton = page.locator("#submitbutton");
    this.updateBillingLink = page.locator("#chargeBee");
    this.errorUserName = page.locator("#userName-error");
    this.errorPassword = page.locator("#pwd-error");
    this.invalidAccountError = page.locator(
      "//div[contains(text(),'Account Not Found / Invalid Password')]"
    );
    this.subscriptionHeader = page.locator("#cb-header-title");
    this.landingWiseLogo = page.locator(".max-h-75px");
  }

  /** Actions */
  async fillUsername(username: string) {
    await this.usernameField.fill(username);
  }

  async fillPassword(password: string) {
    await this.passwordField.fill(password);
  }

  async clickLogin() {
    await this.loginButton.click();
  }

  async login(username: string, password: string) {
    const app = new AppManager(this.page);
    app.utilities.logStep(`Signing in with username: ${username}`);
    await this.fillUsername(username);
    await this.fillPassword(password);
    app.utilities.logStep("Clicking login button");
    await this.clickLogin();
  }

  /** Validations (using fixture data) */
  async validateLoginPageUI(data: any) {
    await expect(this.loginForm).toBeVisible();
    await expect(this.loginHeader).toHaveText(data.messages.header);

    await expect(this.usernameField).toHaveAttribute(
      "placeholder",
      data.messages.placeholders.email
    );
    await expect(this.passwordField).toHaveAttribute(
      "placeholder",
      data.messages.placeholders.password
    );

    await expect(this.forgotPasswordLink).toHaveText(
      data.messages.links.forgotPassword
    );
    await expect(this.loginButton).toHaveText(data.messages.buttons.login);
    await expect(this.updateBillingLink).toHaveText(
      data.messages.links.updateBilling
    );
  }

  async validateLoginPageErrorMessage(data: any) {
    await this.clickLogin();
    await expect(this.errorUserName).toHaveText(
      data.messages.errors.emptyEmail
    );
    await expect(this.errorPassword).toHaveText(
      data.messages.errors.emptyPassword
    );
  }

  async validateLoginWithInvalidEmailAndPassword(data: any) {
    await this.fillUsername(data.login.invalidEmail);
    await this.fillPassword(data.login.invalidPassword);
    await this.clickLogin();
    await expect(this.invalidAccountError).toHaveText(
      data.messages.errors.invalidCredentials
    );
  }

  async validateLoginWithValidEmailAndInvalidPassword(data: any) {
    await this.fillPassword(data.login.invalidPassword);
    await this.clickLogin();
    await expect(this.errorUserName).toHaveText(
      data.messages.errors.emptyEmail
    );

    await this.fillUsername(data.login.validEmail);
    await this.clickLogin();
    await expect(this.invalidAccountError).toHaveText(
      data.messages.errors.invalidCredentials
    );
  }

  async validateLoginWithValidEmailAndPassword(data: any) {
    await this.fillUsername(data.login.validEmail);
    await this.fillPassword(data.login.validPassword);
    await this.clickLogin();
    await expect(this.page).toHaveURL(/backoffice\/dashboard/);
  }

  async validateForgetPasswordLink(data: any) {
    await this.forgotPasswordLink.click();
    const forgotHeader = this.page.locator(
      "//h3[normalize-space()='Forgotten Password ?']"
    );
    await expect(forgotHeader).toHaveText(
      data.messages.links.forgotPasswordHeader
    );
  }

  async validateUpdateBillingLink(data: any) {
    await this.updateBillingLink.click();
    await this.page.waitForTimeout(2000);
    let mainFrame = this.page.frameLocator("#cb-frame");
    await expect(mainFrame.locator(this.subscriptionHeader)).toHaveText(
      data.messages.links.subscriptionHeader
    );
  }

  async validateLandingWiseHomePageNavigation() {
    await this.landingWiseLogo.click();
    await expect(this.page).toHaveURL(/lendingwise.com/);
  }
}
