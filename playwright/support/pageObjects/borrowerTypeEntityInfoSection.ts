import { expect, Locator, Page } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";

interface EntityData {
    entity_Name: string;
    tradeName_DBA: string;
    entity_type: string;
    business_License_Number: string;
    DBA_Name: string,
    prior_Business_Name: string,
    organizational_Ref: string,
    website_Address: string,
    business_Phone: string,
    state_Issued: string,
    other_States_Operating_In: string,
    federal_tax_idein: string,
    stats_of_formation: string,
    formation_date: string,
    time_in_Business: string
}

export class borrowerTypeEntityInfoSection {
    private page: Page;
    private borrowerTypeInputField: Locator;
    private borrowerTypeInputFieldSection: Locator;
    private corporateSecretaryName: Locator;
    private trustInputFieldSection: Locator;
    private trustTypeLabel: Locator;
    private trustSelectionInputField: Locator;
    private retirementInputFieldSection: Locator;
    private retirementTypeLabel: Locator;
    private retirementSelectionInputField: Locator;
    private entityNameInputField: Locator;
    private entitiNameInput: Locator;
    private entityNameSection: Locator;
    private tradeNameOrDBASection: Locator;
    private tradeNameOrDBAInputField: Locator;
    private tradeNameOrDBAInputFieldHeading: Locator;
    private entityNameInputFieldHeading: Locator;
    private entityTypeSelectionOptionField: Locator;
    private entityTypeSelectionOptionFieldHeading: Locator;
    private entityTypeSelectionOptionFieldOptionSelection: Locator;

    private businessLicenseNumberSection: Locator;
    private businessLicenseNumberSectionHeading: Locator;
    private businessLicenseNumberSectionInputField: Locator;

    private DBANameSection: Locator;
    private DBANameSectionHeading: Locator;
    private DBANameSectionInputField: Locator;

    private priorBusinessNameSection: Locator;
    private priorBusinessNameSectionHeading: Locator;
    private priorBusinessNameSectionInputField: Locator;

    private organizationalRefSection: Locator;
    private organizationalRefSectionHeading: Locator;
    private organizationalRefSectionInputField: Locator;

    private websiteAddressSection: Locator;
    private websiteAddressSectionHeading: Locator;
    private websiteAddressSectionInputField: Locator;

    private businessPhoneSection: Locator;
    private businessPhoneSectionHeading: Locator;
    private businessPhoneSectionInputField: Locator;

    private stateIssuedSelectionOptionField: Locator;
    private stateIssuedSelectionOptionFieldHeading: Locator;
    private stateIssuedSelectionOptionFieldOptionSelection: Locator;

    private otherStatesOperatingInSelectionOptionField: Locator;
    private otherStatesOperatingInSelectionOptionFieldHeading: Locator;
    private otherStatesOperatingInSelectionOptionFieldOptionSelection: Locator;

    private federalTaxIdEinSection: Locator;
    private federalTaxIdEinSectionHeading: Locator;
    private federalTaxIdEinSectionInputField: Locator;

    private stateOfFormationSelectionOptionField: Locator;
    private stateOfFormationSelectionOptionFieldHeading: Locator;
    private stateOfFormationSelectionFieldOptionSelection: Locator;

    private dateOfFormationField: Locator;
    private dateOfFormationFieldHeading: Locator;
    private dateOfFormationFieldOptionSelection: Locator;

    private timeinBusinessSelectionOptionField: Locator;
    private timeinBusinessSelectionOptionFieldHeading: Locator;
    private timeinBusinessSelectionOptionSelection: Locator;
    private statesRegisteredInOptionFieldHeading: Locator;
    private statesRegisteredInSelectionOptionField: Locator;

    private issuanceDateField: Locator;
    private issuanceDateFieldHeading: Locator;
    private issuanceDateFieldOptionSelection: Locator;

    private expirationDateField: Locator;
    private expirationDateFieldOptionSelection: Locator;
    private expirationDateFieldHeading: Locator;

    constructor(page: Page) {
        this.page = page;
        this.borrowerTypeInputField = page.locator("#borrowerType");
        this.borrowerTypeInputFieldSection = page.locator('div.borrowerType_disp:nth-child(1)');
        this.corporateSecretaryName = page.locator("#corporateSecretaryName");
        this.trustInputFieldSection = page.locator("div.borrowerType_disp:nth-child(2) > div:nth-child(1)");
        this.trustTypeLabel = page.locator("div.borrowerType_disp:nth-child(2) > div:nth-child(1) > label:nth-child(1)");
        this.trustSelectionInputField = page.locator("#trustType");
        this.retirementInputFieldSection = page.locator("div.borrowerType_disp:nth-child(3) > div:nth-child(1)");
        this.retirementTypeLabel = page.locator("div.borrowerType_disp:nth-child(3) > div:nth-child(1) > label:nth-child(1)");
        this.retirementSelectionInputField = page.locator("#retirementEntity");
        this.entityNameInputField = page.locator(".entitySec > div:nth-child(1)");
        this.entitiNameInput = page.locator(".entitySec > div:nth-child(1)>input");
        this.entityNameSection = page.locator("div.BenGbiTitle:nth-child(2)");
        this.entityNameInputFieldHeading = page.locator("div.BenGbiTitle:nth-child(2) > label:nth-child(1)");
        this.tradeNameOrDBASection = page.locator("div.BenGbiTitle:nth-child(3)");
        this.tradeNameOrDBAInputField = page.locator("div.BenGbiTitle:nth-child(3)>div>input");
        this.tradeNameOrDBAInputFieldHeading = page.locator("div.BenGbiTitle:nth-child(3) > label:nth-child(1)");
        this.entityTypeSelectionOptionField = page.locator("div.BenGbiTitle:nth-child(4)");
        this.entityTypeSelectionOptionFieldHeading = page.locator("div.BenGbiTitle:nth-child(4) > label:nth-child(1)");
        this.entityTypeSelectionOptionFieldOptionSelection = page.locator("div.BenGbiTitle:nth-child(4) > div:nth-child(2) > select:nth-child(1)");

        this.businessLicenseNumberSection = page.locator("div.BenGbiTitle:nth-child(10)");
        this.businessLicenseNumberSectionHeading = page.locator("div.BenGbiTitle:nth-child(10)>label");
        this.businessLicenseNumberSectionInputField = page.locator("div.BenGbiTitle:nth-child(10)>div>input");

        this.DBANameSection = page.locator("div.BenGbiTitle:nth-child(15)");
        this.DBANameSectionHeading = page.locator("div.BenGbiTitle:nth-child(15)>label");
        this.DBANameSectionInputField = page.locator("div.BenGbiTitle:nth-child(15)>div>input");

        this.priorBusinessNameSection = page.locator("div.BenGbiTitle:nth-child(16)");
        this.priorBusinessNameSectionHeading = page.locator("div.BenGbiTitle:nth-child(16)>label");
        this.priorBusinessNameSectionInputField = page.locator("div.BenGbiTitle:nth-child(16)>div>input");

        this.organizationalRefSection = page.locator("div.BenGbiTitle:nth-child(17)");
        this.organizationalRefSectionHeading = page.locator("div.BenGbiTitle:nth-child(17)>label");
        this.organizationalRefSectionInputField = page.locator("div.BenGbiTitle:nth-child(17)>div>input");

        this.websiteAddressSection = page.locator("div.BenGbiTitle:nth-child(19)");
        this.websiteAddressSectionHeading = page.locator("div.BenGbiTitle:nth-child(19)>label");
        this.websiteAddressSectionInputField = page.locator("div.BenGbiTitle:nth-child(19)>div>input");

        this.businessPhoneSection = page.locator("div.BenGbiTitle:nth-child(18)");
        this.businessPhoneSectionHeading = page.locator("div.BenGbiTitle:nth-child(18)>label");
        this.businessPhoneSectionInputField = page.locator("div.BenGbiTitle:nth-child(18)>div>input");

        this.stateIssuedSelectionOptionField = page.locator("div.BenGbiTitle:nth-child(13)");
        this.stateIssuedSelectionOptionFieldHeading = page.locator("div.BenGbiTitle:nth-child(13)>label");
        this.stateIssuedSelectionOptionFieldOptionSelection = page.locator("div.BenGbiTitle:nth-child(13)>div>select");

        this.otherStatesOperatingInSelectionOptionField = page.locator("div.BenGbiTitle:nth-child(14)");
        this.otherStatesOperatingInSelectionOptionFieldHeading = page.locator("div.BenGbiTitle:nth-child(14)>label");
        this.otherStatesOperatingInSelectionOptionFieldOptionSelection = page.locator("#operatingStates_chosen");

        this.federalTaxIdEinSection = page.locator("div.BenGbiTitle:nth-child(9)");
        this.federalTaxIdEinSectionHeading = page.locator("div.BenGbiTitle:nth-child(9)>label");
        this.federalTaxIdEinSectionInputField = page.locator("div.BenGbiTitle:nth-child(9)>div>input");

        this.stateOfFormationSelectionOptionField = page.locator("div.BenGbiTitle:nth-child(7)");
        this.stateOfFormationSelectionOptionFieldHeading = page.locator("div.BenGbiTitle:nth-child(7)>label");
        this.stateOfFormationSelectionFieldOptionSelection = page.locator("div.BenGbiTitle:nth-child(7)>div>select");

        this.dateOfFormationField = page.locator("div.BenGbiTitle:nth-child(5)");
        this.dateOfFormationFieldHeading = page.locator("div.BenGbiTitle:nth-child(5)>label");
        this.dateOfFormationFieldOptionSelection = page.locator("#dateOfFormation");


        this.timeinBusinessSelectionOptionField = page.locator("div.BenGbiTitle:nth-child(6)")
        this.timeinBusinessSelectionOptionFieldHeading = page.locator("div.BenGbiTitle:nth-child(6)>label")
        this.timeinBusinessSelectionOptionSelection = page.locator("div.BenGbiTitle:nth-child(6)>div");

        this.statesRegisteredInSelectionOptionField = page.locator("#statesRegisterdIn_chosen");
        this.statesRegisteredInOptionFieldHeading = page.locator("div.BenGbiTitle:nth-child(8) > label:nth-child(1)");

        this.issuanceDateField = page.locator("div.BenGbiTitle:nth-child(11)");
        this.issuanceDateFieldHeading = page.locator("div.BenGbiTitle:nth-child(11)>label");
        this.issuanceDateFieldOptionSelection = page.locator("#borLicenseIssuance");

        this.expirationDateField = page.locator("div.BenGbiTitle:nth-child(12)");
        this.expirationDateFieldHeading = page.locator("div.BenGbiTitle:nth-child(12)>label");
        this.expirationDateFieldOptionSelection = page.locator("#borLicenseExpiration")
    }

    async selectTheOptionForborroweType(option: string) {
        const app = new AppManager(this.page);
        await expect(this.borrowerTypeInputFieldSection).toBeVisible();
        await this.borrowerTypeInputField.click();
        await app.utilities.selectFromDropdown(this.page, this.borrowerTypeInputField, option);
    }
    async validateCorporateSecretaryNameInputField(corporateSecretaryName: string) {
        await expect(this.corporateSecretaryName).toBeVisible();
        await this.corporateSecretaryName.fill(corporateSecretaryName);
        await expect(this.corporateSecretaryName).toHaveText(corporateSecretaryName);
    }
    async validateTrustInputField(value: string) {
        const app = new AppManager(this.page);
        await expect(this.trustInputFieldSection).toBeVisible();
        await expect(this.trustTypeLabel).toBeVisible();
        await expect(this.trustTypeLabel).toHaveText("Trust Type");
        await app.utilities.selectFromDropdown(this.page, this.trustSelectionInputField, value)
        await expect(this.trustSelectionInputField).toContainText(value);
    }
    async validateRetirementInputField(value: string) {
        const app = new AppManager(this.page);
        await expect(this.retirementInputFieldSection).toBeVisible();
        await expect(this.retirementTypeLabel).toBeVisible();
        await expect(this.retirementTypeLabel).toHaveText("Retirement Entity");
        await app.utilities.selectFromDropdown(this.page, this.retirementSelectionInputField, value)
        await expect(this.retirementSelectionInputField).toContainText(value);
    }
    async validateEntityInputField(data: EntityData) {
        const app = new AppManager(this.page);

        app.utilities.logStep("Validate adding entity name");
        await expect(this.entityNameSection).toBeVisible();
        await expect(this.entityNameInputFieldHeading).toHaveText("Entity Name");
        await expect(this.entityNameInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_Name) {
            await this.entitiNameInput.fill(data.entity_Name);
            await expect(this.entitiNameInput).toHaveValue(data.entity_Name);
            app.utilities.logStep("-----> Entity name is added correctly");
        } else {
            app.utilities.logStep("-----> Entity name is not provided");
        }


        app.utilities.logStep("validate the trade name/DBA section");
        await expect(this.tradeNameOrDBASection).toBeVisible();
        await expect(this.tradeNameOrDBAInputFieldHeading).toHaveText("Trade Name/DBA");
        await expect(this.tradeNameOrDBAInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_Name) {
            await this.tradeNameOrDBAInputField.fill(data.tradeName_DBA);
            await expect(this.tradeNameOrDBAInputField).toHaveValue(data.tradeName_DBA);
            app.utilities.logStep("-----> Trade Name Or DBA name is added correctly");
        } else {
            app.utilities.logStep("-----> Trade Name Or DBA  name is not provided");
        }

        app.utilities.logStep("validate the Entity Type option selection");
        await expect(this.entityTypeSelectionOptionField).toBeVisible();
        await expect(this.entityTypeSelectionOptionFieldHeading).toHaveText("Entity Type");
        await expect(this.entityTypeSelectionOptionFieldOptionSelection).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await app.utilities.selectFromDropdown(this.page, this.entityTypeSelectionOptionFieldOptionSelection, data.entity_type, true);
            app.utilities.logStep("-----> Entity Type is added correctly");
        } else {
            app.utilities.logStep("-----> Entity Type is not provided");
        }

        // [Date Of Formation is pending]
        app.utilities.logStep("validate the Date Of Formation selection");
        await expect(this.dateOfFormationField).toBeVisible();
        await expect(this.dateOfFormationFieldHeading).toHaveText("Date Of Formation");
        await expect(this.dateOfFormationFieldOptionSelection).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await this.dateOfFormationFieldOptionSelection.click();
            await this.dateOfFormationFieldOptionSelection.press("Control+A");
            await this.dateOfFormationFieldOptionSelection.press("Backspace");
            await this.dateOfFormationFieldOptionSelection.fill(data.formation_date);
            app.utilities.logStep("-----> Date Of Formation is added correctly");
        } else {
            app.utilities.logStep("-----> Date Of Formation is not provided");
        }

        // [Time in Business is pending]
        app.utilities.logStep("validate the Time in Business option selection");
        await expect(this.timeinBusinessSelectionOptionField).toBeVisible();
        await expect(this.timeinBusinessSelectionOptionFieldHeading).toHaveText("Time in Business");
        //await expect(this.timeinBusinessSelectionOptionSelection).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await app.utilities.selectFromSingleSelect(this.page, this.timeinBusinessSelectionOptionSelection, data.time_in_Business);
            app.utilities.logStep("-----> Time in Business is added correctly");
        } else {
            app.utilities.logStep("-----> Time in Business is not provided");
        }

        // [State Of Formation]
        app.utilities.logStep("validate the State Of Formation selection");
        await expect(this.stateOfFormationSelectionOptionField).toBeVisible();
        await expect(this.stateOfFormationSelectionOptionFieldHeading).toHaveText("State Of Formation");
        await expect(this.stateOfFormationSelectionFieldOptionSelection).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await app.utilities.selectFromDropdown(this.page, this.stateOfFormationSelectionFieldOptionSelection, data.stats_of_formation, true);
            app.utilities.logStep("-----> State Of Formation is added correctly");
        } else {
            app.utilities.logStep("-----> State Of Formation is not provided");
        }

        // [States Registered In is pending]
        app.utilities.logStep("validate the States Registered In option selection");
        await expect(this.statesRegisteredInSelectionOptionField).toBeVisible();
        await expect(this.statesRegisteredInOptionFieldHeading).toHaveText("States Registered In");
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await app.utilities.selectFromSingleSelect(this.page, this.statesRegisteredInSelectionOptionField, data.other_States_Operating_In, false);
            app.utilities.logStep("-----> States option is added correctly");
        } else {
            app.utilities.logStep("-----> States option is not provided");
        }

        // [Federal Tax ID/EIN]
        app.utilities.logStep("Validate the Federal Tax ID/EIN section");
        await expect(this.federalTaxIdEinSection).toBeVisible();
        await expect(this.federalTaxIdEinSectionHeading).toHaveText("Federal Tax ID/EIN");
        await expect(this.federalTaxIdEinSectionInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.business_Phone) {
            await this.federalTaxIdEinSectionInputField.fill(data.federal_tax_idein);
            await expect(this.federalTaxIdEinSectionInputField).toHaveValue(data.federal_tax_idein);
            app.utilities.logStep("-----> Federal Tax ID/EIN is added correctly");
        } else {
            app.utilities.logStep("-----> Federal Tax ID/EIN is not provided");
        }

        // Business License Number 
        app.utilities.logStep("Validate the Business License Number section");
        await expect(this.businessLicenseNumberSection).toBeVisible();
        await expect(this.businessLicenseNumberSectionHeading).toHaveText("Business License Number");
        await expect(this.businessLicenseNumberSectionInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_Name) {
            await this.businessLicenseNumberSectionInputField.fill(data.business_License_Number);
            await expect(this.businessLicenseNumberSectionInputField).toHaveValue(data.business_License_Number);
            app.utilities.logStep("-----> Business License Number is added correctly");
        } else {
            app.utilities.logStep("-----> Business License Number is not provided");
        }

        // [Issuance Date is pending]
        app.utilities.logStep("validate the Issuance Date selection");
        await expect(this.issuanceDateField).toBeVisible();
        await expect(this.issuanceDateFieldHeading).toHaveText("Issuance Date");
        await expect(this.issuanceDateFieldOptionSelection).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await this.issuanceDateFieldOptionSelection.click();
            await this.issuanceDateFieldOptionSelection.press("Control+A");
            await this.issuanceDateFieldOptionSelection.press("Backspace");
            await this.issuanceDateFieldOptionSelection.fill(data.formation_date);
            app.utilities.logStep("-----> Issuance Date is added correctly");
        } else {
            app.utilities.logStep("-----> Issuance Date is not provided");
        }

        // [Expiration Date  is pending]
        app.utilities.logStep("validate the Expiration Date selection");
        await expect(this.expirationDateField).toBeVisible();
        await expect(this.expirationDateFieldHeading).toHaveText("Expiration Date");
        await expect(this.expirationDateFieldOptionSelection).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await this.expirationDateFieldOptionSelection.click();
            await this.expirationDateFieldOptionSelection.press("Control+A");
            await this.expirationDateFieldOptionSelection.press("Backspace");
            await this.expirationDateFieldOptionSelection.fill(data.formation_date);
            app.utilities.logStep("-----> Expiration Date is added correctly");
        } else {
            app.utilities.logStep("-----> Expiration Date is not provided");
        }


        // [State Issued]
        app.utilities.logStep("validate the State Issued option selection");
        await expect(this.stateIssuedSelectionOptionField).toBeVisible();
        await expect(this.stateIssuedSelectionOptionFieldHeading).toHaveText("State Issued");
        await expect(this.stateIssuedSelectionOptionFieldOptionSelection).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await app.utilities.selectFromDropdown(this.page, this.stateIssuedSelectionOptionFieldOptionSelection, data.state_Issued, true);
            app.utilities.logStep("-----> State Issued is added correctly");
        } else {
            app.utilities.logStep("-----> State Issued is not provided");
        }

        // [Other States Operating In]
        app.utilities.logStep("validate the Other States option selection");
        await expect(this.otherStatesOperatingInSelectionOptionField).toBeVisible();
        await expect(this.otherStatesOperatingInSelectionOptionFieldHeading).toHaveText("Other States Operating In");
        // [need to add the validation if value present previously in form]
        if (data.entity_type) {
            await app.utilities.scrollToElement2(this.page, this.otherStatesOperatingInSelectionOptionFieldHeading);
            await this.otherStatesOperatingInSelectionOptionFieldOptionSelection.click();
            await this.page.locator('#operatingStates_chosen').getByText('Alaska').click()
            app.utilities.logStep("-----> Other States Operating In option is added correctly");
        } else {
            app.utilities.logStep("-----> Other States Operating In option is not provided");
        }

        // DBA Name(s) 
        app.utilities.logStep("Validate the  DBA Name(s) section");
        await expect(this.DBANameSection).toBeVisible();
        await expect(this.DBANameSectionHeading).toHaveText("DBA Name(s)");
        await expect(this.DBANameSectionInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_Name) {
            await this.DBANameSectionInputField.fill(data.DBA_Name);
            await expect(this.DBANameSectionInputField).toHaveValue(data.DBA_Name);
            app.utilities.logStep("-----> DBA Name(s) is added correctly");
        } else {
            app.utilities.logStep("-----> DBA Name(s) is not provided");
        }

        // Prior Business Name(s)
        app.utilities.logStep("Validate the Prior Business Name section");
        await expect(this.priorBusinessNameSection).toBeVisible();
        await expect(this.priorBusinessNameSectionHeading).toHaveText("Prior Business Name(s)");
        await expect(this.priorBusinessNameSectionInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_Name) {
            await this.priorBusinessNameSectionInputField.fill(data.prior_Business_Name);
            await expect(this.priorBusinessNameSectionInputField).toHaveValue(data.prior_Business_Name);
            app.utilities.logStep("-----> Prior Business Name is added correctly");
        } else {
            app.utilities.logStep("-----> Prior Business Name is not provided");
        }

        // Organizational Ref # 
        app.utilities.logStep("Validate the Organizational Ref # section");
        await expect(this.organizationalRefSection).toBeVisible();
        await expect(this.organizationalRefSectionHeading).toHaveText("Organizational Ref #");
        await expect(this.organizationalRefSectionInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_Name) {
            await this.organizationalRefSectionInputField.fill(data.organizational_Ref);
            await expect(this.organizationalRefSectionInputField).toHaveValue(data.organizational_Ref);
            app.utilities.logStep("-----> Organizational Ref # is added correctly");
        } else {
            app.utilities.logStep("-----> Organizational Ref # is not provided");
        }

        // [Business Phone is pending]
        app.utilities.logStep("Validate the Business Phone section");
        await expect(this.businessPhoneSection).toBeVisible();
        await expect(this.businessPhoneSectionHeading).toHaveText("Business Phone");
        await expect(this.businessPhoneSectionInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_Name) {
            // await this.businessPhoneSectionInputField.click();
            await this.businessPhoneSectionInputField.fill(data.business_Phone);
            await expect(this.businessPhoneSectionInputField).toHaveValue(data.business_Phone);
            app.utilities.logStep("-----> Business Phone is added correctly");
        } else {
            app.utilities.logStep("-----> Business Phone is not provided");
        }

        // Website Address 
        app.utilities.logStep("Validate the Website Address section");
        await expect(this.websiteAddressSection).toBeVisible();
        await expect(this.websiteAddressSectionHeading).toHaveText("Website Address");
        await expect(this.websiteAddressSectionInputField).toBeVisible();
        // [need to add the validation if value present previously in form]
        if (data.entity_Name) {
            await this.websiteAddressSectionInputField.fill(data.website_Address);
            await expect(this.websiteAddressSectionInputField).toHaveValue(data.website_Address);
            app.utilities.logStep("-----> Website Address is added correctly");
        } else {
            app.utilities.logStep("-----> Website Address is not provided");
        }
    }
}




