import { expect, Locator, <PERSON> } from "@playwright/test";
import { AppManager } from "../AppManager/PageObjectManager";

export class sbaquestionsSection {
    private page: Page;
    //SBA Questions
    private sbaquestionscard: any;
    private monthlyPayroll: any;
    private purposechechbox: any;
    private listValueOfInputFields: Locator;
    private quarterExpLoss2020: any;
    private quarterExpLoss2021: any;
    private criminalinfoyes: any;
    private criminalinfono: any;
    private criminaldetailexplainationsection: any;
    private criminaldetailexplainationfeild: any;
    private haveyouarrestedyes: any;
    private haveyouarrestedno: any;
    private arresteddetailexplainationsection: any;
    private arresteddetailexplainationfeild: any;
    private criminaloffenceyes: any;
    private criminaloffenceno: any;
    private criminaloffencedetailexplainationsection: any;
    private criminaloffencedetailexplainationfield: any;
    private certifieddevelopmentyes: any;
    private certifieddevelopmentno: any;
    private certifieddevelopmentexpsection: any;
    private certifieddevelopmentexpfield: any;
    private sBAFranchiseDirectoryyes: any;
    private sBAFranchiseDirectoryno: any;
    private sBAFranchiseDirectoryexpsection: any;
    private sBAFranchiseDirectoryexpfield: any;
    private smallBusinessApplicantyes: any;
    private smallBusinessApplicantno: any;
    private smallBusinessApplicantexpsection: any;
    private smallBusinessApplicantexpfield: any;
    private smallBusinessApplicantBankruptcyyes: any;
    private smallBusinessApplicantBankruptcyno: any;
    private smallBusinessApplicantBankruptcyexpsection: any;
    private smallBusinessApplicantBankruptcyexpfield: any;
    private closeapplicantbankruptcy: any;
    private smallBusinessApplicantLegalYes: any;
    private smallBusinessApplicantLegalNo: any;
    private smallBusinessApplicantLegalexpsection: any;
    private smallBusinessApplicantLegalexpfield: any;
    private debarredSuspendedFederalYes: any;
    private debarredSuspendedFederalNo: any;
    private debarredSuspendedFederalexpsection: any;
    private debarredSuspendedFederalexpfield: any;
    private childSupportEnforcementServicesYes: any;
    private childSupportEnforcementServicesNo: any;
    private childSupportEnforcementServicesexpsectiion: any;
    private childSupportEnforcementServicesexpfield: any;
    private sbaLoanSectionField: any;
    private sbaLoanYes: any;
    private sbaLoanNo: any;
    private sbaLoanDelinquentSection: any;
    private sbaLoanDefaultLossSection: any;
    private sbaLoanDelinquentExplanationField: any;
    private sbaLoanDefaultLossExplanationField: any;
    private sbaLoanDelinquentYes: any;
    private sbaLoanDelinquentNo: any;
    private sbaLoanDefaultLossYes: any;
    private sbaLoanDefaultLossNo: any;
    private smallBusinessApplicantExportYes: any;
    private smallBusinessApplicantExportNo: any;
    private smallBusinessApplicantExportamountsec: any;
    private smallBusinessApplicantExportamountfield: any;
    private smallBusinessApplicantPackerYes: any;
    private smallBusinessApplicantPackerNo: any;
    private smallBusinessApplicantPackerexpsection: any;
    private smallBusinessApplicantPackerexpfield: any;
    private smallBusinessApplicantRevenueYes: any;
    private smallBusinessApplicantRevenueNo: any;
    private smallBusinessApplicantRevenueexpsection: any;
    private smallBusinessApplicantRevenuefield: any;
    private noSBAempHouseHoldMemberYes: any;
    private noSBAempHouseHoldMemberNo: any;
    private noSBAempHouseHoldMemberexpsection: any;
    private noSBAempHouseHoldMemberexpfield: any;
    private noFormerSBAempSeparatedYes: any;
    private noFormerSBAempSeparatedNo: any;
    private noFormerSBAempSeparatedexpsection: any;
    private noFormerSBAempSeparatedexpfield: any;
    private noMemberSoleProprietorYes: any;
    private noMemberSoleProprietorNo: any;
    private noMemberSoleProprietorexpsection: any;
    private noMemberSoleProprietorexpfield: any;
    private noGovEmpGS13Yes: any;
    private noGovEmpGS13No: any;
    private noGovEmpGS13expsection: any;
    private noGovEmpGS13expfield: any;
    private noMemberSmallBuinessAdvisoryYes: any;
    private noMemberSmallBuinessAdvisoryNo: any;
    private noMemberSmallBuinessAdvisoryexpsection: any;
    private noMemberSmallBuinessAdvisoryexpfield: any;
    private haveControlledBankruptcyProtectionYes: any;
    private haveControlledBankruptcyProtectionNo: any;
    private haveControlledBankruptcyProtectionexpsection: any;
    private haveControlledBankruptcyProtectionexpfield: any;
    private applicantsPayrollCalculationYes: any;
    private applicantsPayrollCalculationNo: any;
    private applicantsPayrollCalculationexpsection: any;
    private applicantsPayrollCalculationexpfield: any;
    private estimatedMonthlyPayrollYes: any;
    private estimatedMonthlyPayrollNo: any;
    private estimatedMonthlyPayrollexpsection: any;
    private estimatedMonthlyPayrollexpfield: any;
    private lossToTheGovernmentYes: any;
    private lossToTheGovernmentNo: any;
    private lossToTheGovernmentexpsection: any;
    private lossToTheGovernmentexpfield: any;
    private businessControlLegalActionYes: any;
    private businessControlLegalActionNo: any;
    private businessControlLegalActionexpsection: any;
    private businessControlLegalActionexpfield: any;
    private sbaEconomicInjuryYes: any;
    private sbaEconomicInjuryNo: any;
    private sbaEconomicInjuryexpsection: any;
    private sbaEconomicInjuryexpfield: any;
    private sbaEconomicInjuryLoanAmt: any;
    private haveOwnershipAffiliateYes: any;
    private haveOwnershipAffiliateNo: any;
    private haveOwnershipAffiliateexpsection: any;
    private haveOwnershipAffiliateexpfield: any;
    private removebusinessInfo: any;
    private addOtherBusinessInfo: any;
    private companyname: any;
    private taxid: any;
    private legalowners: any;
    private idofemp: any;
    private yrinbusiness: any;
    private appliedforloan: any;
    private decsription: any;
    private appliedforloanYes: any;
    private appliedforloanNo: any;
    private w2empfield: any;
    private w2issuefield: any;
    private companystarted: any;
    private q1_2019_input: any;
    private q2_2019_input: any;
    private q3_2019_input: any;
    private q4_2019_input: any;
    private q1_2020_input: any;
    private q2_2020_input: any;
    private q3_2020_input: any;
    private q4_2020_input: any;
    private q1_2021_input: any;
    private q2_2021_input: any;
    private q3_2021_input: any;
    private q4_2021_input: any;

    private q1_2019_input2: any;
    private q2_2019_input2: any;
    private q3_2019_input2: any;
    private q4_2019_input2: any;
    private q1_2020_input2: any;
    private q2_2020_input2: any;
    private q3_2020_input2: any;
    private q4_2020_input2: any;
    private q1_2021_input2: any;
    private q2_2021_input2: any;
    private q3_2021_input2: any;
    private q4_2021_input2: any;

    private govOrderSuspensionField: any;
    private govOrderSuspensionNo: any;
    private govOrderSuspensionPartial: any;
    private govOrderSuspensionFull: any;
    private govOrderImpactedExplanation: any;
    private govOrderAffectedQuarters: any;

    private vendorSuspensionNoOption: any;
    private vendorSuspensionNotSureOption: any;
    private vendorSuspensionPartialOption: any;
    private vendorSuspensionFullOption: any;
    private vendorImpactExplanationField: any;
    private vendorAffectedQuartersField: any;

    private havePPPLoanForgivenessDraw1: any;
    private havePPPLoanForgivenessDraw2: any;
    private payrollservice: any;
    private empretentionYes: any;
    private empretentionNo: any;
    private quarterandcompanysec: any;
    private ppp_draw1_input: any;
    private ppp_draw2_input: any;
    private employeeFamilyOwnerfield: any;
    private otherbusiness: any;
    private otherexistbusiness: any;
    private pppdrawforgive: any;
    private ppp_draw1_forgiveness_yes: any;
    private ppp_draw1_forgiveness_no: any;
    private ppp_draw2_forgiveness_yes: any;
    private ppp_draw2_forgiveness_no: any;

    constructor(page: Page) {
        this.page = page;

        this.quarterExpLoss2020 = page.locator('#quarterExperiencedLoss2020_chosen > ul > li > input');
        this.quarterExpLoss2021 = page.locator('.quarterExperiencedLoss2021_disp > div:nth-child(1) > label:nth-child(1)');
        this.listValueOfInputFields = page.locator(".chosen-drop .chosen-results li");
        this.q1_2019_input = page.locator('//*[@id="q12019"]');
        this.q2_2019_input = page.locator('//*[@id="q22019"]');
        this.q3_2019_input = page.locator('//*[@id="q32019"]');
        this.q4_2019_input = page.locator('//*[@id="q42019"]');
        this.q1_2020_input = page.locator('//*[@id="q12020"]');
        this.q2_2020_input = page.locator('//*[@id="q22020"]');
        this.q3_2020_input = page.locator('//*[@id="q32020"]');
        this.q4_2020_input = page.locator('//*[@id="q42020"]');
        this.q1_2021_input = page.locator('//*[@id="q12021"]');
        this.q2_2021_input = page.locator('//*[@id="q22021"]');
        this.q3_2021_input = page.locator('//*[@id="q32021"]');
        this.q4_2021_input = page.locator('//*[@id="q42021"]');
        this.q1_2019_input2 = page.locator('//*[@id="q12019v"]');
        this.q2_2019_input2 = page.locator('//*[@id="q22019v"]');
        this.q3_2019_input2 = page.locator('//*[@id="q32019v"]');
        this.q4_2019_input2 = page.locator('//*[@id="q42019v"]');
        this.q1_2020_input2 = page.locator('//*[@id="q12020v"]');
        this.q2_2020_input2 = page.locator('//*[@id="q22020v"]');
        this.q3_2020_input2 = page.locator('//*[@id="q32020v"]');
        this.q4_2020_input2 = page.locator('//*[@id="q42020v"]');
        this.q1_2021_input2 = page.locator('//*[@id="q12021v"]');
        this.q2_2021_input2 = page.locator('//*[@id="q22021v"]');
        this.q3_2021_input2 = page.locator('//*[@id="q32021v"]');
        this.q4_2021_input2 = page.locator('//*[@id="q42021v"]');
        this.pppdrawforgive = page.locator("//label[contains(text(),'If you received a PPP loan(s), did you receive PPP')]");
        this.ppp_draw1_forgiveness_yes = page.locator("//label[contains(@for,'receivePPPLoanForgivenessDraw1Yes')]");
        this.ppp_draw1_forgiveness_no = page.locator('#receivePPPLoanForgivenessDraw1No');
        this.ppp_draw2_forgiveness_yes = page.locator('#receivePPPLoanForgivenessDraw2Yes')
        this.ppp_draw2_forgiveness_no = page.locator("//label[@for='receivePPPLoanForgivenessDraw2No']")

        this.ppp_draw1_input = page.locator('//*[@id="receivePPPLoanDraw1"]');
        this.ppp_draw2_input = page.locator('//*[@id="receivePPPLoanDraw2"]');
        this.govOrderSuspensionField = page.locator("//label[contains(text(),'Did you operate under full or partial suspension d')]");
        this.govOrderSuspensionNo = page.locator("//label[@for='operateSuspensionNo']//span");
        this.govOrderSuspensionPartial = page.locator("//label[contains(@for,'operateSuspensionPS')]//span");
        this.govOrderSuspensionFull = page.locator("//label[contains(@for,'operateSuspensionFS')]//span");
        this.govOrderImpactedExplanation = page.locator('//*[@id="businessImpact"]');
        this.govOrderAffectedQuarters = page.locator('#quarterEffected_chosen > ul');
        this.vendorSuspensionNoOption = page.locator("//label[contains(@for,'vendorOperateSuspensionNo')]//span");
        this.vendorSuspensionNotSureOption = page.locator("//label[normalize-space()='Not Sure']");
        this.vendorSuspensionPartialOption = page.locator("//label[contains(@for,'vendorOperateSuspensionPS')]//span");
        this.vendorSuspensionFullOption = page.locator("//label[contains(@for,'vendorOperateSuspensionFS')]//span");
        this.vendorImpactExplanationField = page.locator('//*[@id="vendorBusinessImpact"]');
        this.vendorAffectedQuartersField = page.locator('#quarterEffectedVendor_chosen > ul:nth-child(1)');

        this.sbaquestionscard = page.locator('div.card.SBAQCard.SBAQ_row.SBAQ');
        this.havePPPLoanForgivenessDraw1 = page.locator('//*[@id="havePPPLoanForgivenessDraw1"]');
        this.havePPPLoanForgivenessDraw2 = page.locator('//*[@id="havePPPLoanForgivenessDraw2"]');
        this.payrollservice = page.locator('//*[@id="payrollServiceProvider"]');
        this.empretentionYes = page.locator("//label[@for='employeeRetentionCreditYes']//span");
        this.empretentionNo = page.locator("//label[contains(@for,'employeeRetentionCreditNo')]//span");
        this.quarterandcompanysec = page.locator('//*[@id="employeeRetentionCreditCompany"]');
        this.monthlyPayroll = page.locator(".avgMonthlyPayroll_disp > div:nth-child(1) > div:nth-child(2) > div:nth-child(1)>#avgMonthlyPayroll");
        this.purposechechbox = page.locator(".purposeOfTheLoan_disp > div:nth-child(1) > div:nth-child(2) > div:nth-child(1)");
        this.criminalinfoyes = page.locator("//label[contains(@for,'areyouIndictmentCriminalYes')]//span");
        this.criminalinfono = page.locator("//label[contains(@for,'areyouIndictmentCriminalNo')]//span");
        this.criminaldetailexplainationsection = page.locator(".areyouIndictmentCriminalExpl > label:nth-child(1)");
        this.criminaldetailexplainationfeild = page.locator('//*[@id="areyouIndictmentCriminalExpl"]');
        this.haveyouarrestedyes = page.locator("//label[contains(@for,'haveYouArrestedYes')]//span");
        this.haveyouarrestedno = page.locator("//label[contains(@for,'haveYouArrestedNo')]//span");
        this.arresteddetailexplainationsection = page.locator("//label[contains(@for,'haveYouArrestedExpl')]");
        this.arresteddetailexplainationfeild = page.locator('//*[@id="haveYouArrestedExpl"]');
        this.criminaloffenceyes = page.locator("//label[contains(@for,'criminalOffenseYes')]//span");
        this.criminaloffenceno = page.locator("//label[@for='criminalOffenseNo']//span");
        this.criminaloffencedetailexplainationsection = page.locator("//label[contains(@for,'criminalOffenseExpl')]");
        this.criminaloffencedetailexplainationfield = page.locator('//*[@id="criminalOffenseExpl"]');
        this.certifieddevelopmentyes = page.locator("//label[contains(@for,'certifiedDevelopmentYes')]//span");
        this.certifieddevelopmentno = page.locator("//label[contains(@for,'certifiedDevelopmentNo')]//span");
        this.certifieddevelopmentexpsection = page.locator("//label[contains(@for,'certifiedDevelopmentExpl')]");
        this.certifieddevelopmentexpfield = page.locator('//*[@id="certifiedDevelopmentExpl"]');
        this.sBAFranchiseDirectoryyes = page.locator("//label[contains(@for,'smallBusinessApplicantYes')]//span");
        this.sBAFranchiseDirectoryno = page.locator("//label[contains(@for,'smallBusinessApplicantNo')]//span");
        this.sBAFranchiseDirectoryexpsection = page.locator("//label[contains(@for,'smallBusinessApplicantExpl')]");
        this.sBAFranchiseDirectoryexpfield = page.locator('//*[@id="smallBusinessApplicantExpl"]');
        this.smallBusinessApplicantyes = page.locator("//label[@for='smallBusinessApplicantAffilYes']//span");
        this.smallBusinessApplicantno = page.locator("//label[contains(@for,'smallBusinessApplicantAffilNo')]//span");
        this.smallBusinessApplicantexpsection = page.locator("//label[contains(@for,'smallBusinessApplicantAffilExpl')]");
        this.smallBusinessApplicantexpfield = page.locator('//*[@id="smallBusinessApplicantAffilExpl"]');
        this.smallBusinessApplicantBankruptcyyes = page.locator("//label[contains(@for,'smallBusinessApplicantBankruptcyYes')]//span");
        this.smallBusinessApplicantBankruptcyno = page.locator("//label[contains(@for,'smallBusinessApplicantBankruptcyNo')]//span");
        this.smallBusinessApplicantBankruptcyexpsection = page.locator("//label[contains(@for,'smallBusinessApplicantBankruptcyExpl')]");
        this.smallBusinessApplicantBankruptcyexpfield = page.locator('//*[@id="smallBusinessApplicantBankruptcyExpl"]');
        this.closeapplicantbankruptcy = page.locator('#businessBankruptcy_chosen > a:nth-child(1) > abbr:nth-child(2)');
        this.smallBusinessApplicantLegalYes = page.locator("//label[contains(@for,'smallBusinessApplicantLegalYes')]//span");
        this.smallBusinessApplicantLegalNo = page.locator("//label[contains(@for,'smallBusinessApplicantLegalNo')]//span");
        this.smallBusinessApplicantLegalexpsection = page.locator("//label[contains(@for,'smallBusinessApplicantLegalExpl')]");
        this.smallBusinessApplicantLegalexpfield = page.locator('//*[@id="smallBusinessApplicantLegalExpl"]');
        this.debarredSuspendedFederalYes = page.locator("//label[contains(@for,'debarredSuspendedFederalYes')]//span");
        this.debarredSuspendedFederalNo = page.locator("//label[contains(@for,'debarredSuspendedFederalNo')]//span");
        this.debarredSuspendedFederalexpsection = page.locator("//label[contains(@for,'debarredSuspendedFederalExpl')]");
        this.debarredSuspendedFederalexpfield = page.locator('//*[@id="debarredSuspendedFederalExpl"]');
        this.childSupportEnforcementServicesYes = page.locator("//label[@for='childSupportEnforcementServicesYes']//span");
        this.childSupportEnforcementServicesNo = page.locator("//label[contains(@for,'childSupportEnforcementServicesNo')]//span");
        this.childSupportEnforcementServicesexpsectiion = page.locator("//label[contains(@for,'childSupportEnforcementServicesExpl')]");
        this.childSupportEnforcementServicesexpfield = page.locator('//*[@id="childSupportEnforcementServicesExpl"]');
        this.sbaLoanSectionField = page.locator("//label[@for='smallBusinessApplicantLoan']");
        this.sbaLoanYes = page.locator("//label[@for='smallBusinessApplicantLoanYes']//span");
        this.sbaLoanNo = page.locator("//label[contains(@for,'smallBusinessApplicantLoanNo')]//span");
        this.sbaLoanDelinquentSection = page.locator("//label[contains(@for,'anyFinancingDelinquentExpl')]");
        this.sbaLoanDefaultLossSection = page.locator("//label[@for='anyFinancingDefaultExpl']");
        this.sbaLoanDelinquentExplanationField = page.locator('//*[@id="anyFinancingDelinquentExpl"]');
        this.sbaLoanDefaultLossExplanationField = page.locator('//*[@id="anyFinancingDefaultExpl"]');
        this.smallBusinessApplicantExportYes = page.locator("//label[@for='smallBusinessApplicantExportYes']//span");
        this.smallBusinessApplicantExportNo = page.locator("//label[contains(@for,'smallBusinessApplicantExportNo')]//span");
        this.smallBusinessApplicantExportamountsec = page.locator("//label[contains(text(),'Provide the estimated total export sales this loan')]");
        this.smallBusinessApplicantExportamountfield = page.locator('//*[@id="smallBusinessApplicantExportExpl"]');
        this.smallBusinessApplicantPackerYes = page.locator("//label[contains(@for,'smallBusinessApplicantPackerYes')]//span");
        this.smallBusinessApplicantPackerNo = page.locator("//label[contains(@for,'smallBusinessApplicantPackerNo')]//span");
        this.smallBusinessApplicantPackerexpsection = page.locator("//label[contains(@for,'smaillBusinessApplicantPackerExpl')]");
        this.smallBusinessApplicantPackerexpfield = page.locator('//*[@id="smallBusinessApplicantPackerExpl"]');
        this.smallBusinessApplicantRevenueYes = page.locator("//label[@for='smallBusinessApplicantRevenueYes']//span");
        this.smallBusinessApplicantRevenueNo = page.locator("//label[contains(@for,'smallBusinessApplicantRevenueNo')]//span");
        this.smallBusinessApplicantRevenueexpsection = page.locator("//label[contains(@for,'smallBusinessApplicantRevenueExpl')]");
        this.smallBusinessApplicantRevenuefield = page.locator('//*[@id="smallBusinessApplicantRevenueExpl"]');
        this.noSBAempHouseHoldMemberYes = page.locator("//label[@for='noSBAempHouseHoldMemberYes']//span");
        this.noSBAempHouseHoldMemberNo = page.locator("//label[contains(@for,'noSBAempHouseHoldMemberNo')]//span");
        this.noSBAempHouseHoldMemberexpsection = page.locator("//label[contains(@for,'noSBAempHouseHoldMemberExpl')]");
        this.noSBAempHouseHoldMemberexpfield = page.locator('//*[@id="noSBAempHouseHoldMemberExpl"]');
        this.noFormerSBAempSeparatedYes = page.locator("//label[@for='noFormerSBAempSeparatedYes']//span");
        this.noFormerSBAempSeparatedNo = page.locator("//label[contains(@for,'noFormerSBAempSeparatedNo')]//span");
        this.noFormerSBAempSeparatedexpsection = page.locator("//label[contains(@for,'noFormerSBAempSeparatedExpl')]");
        this.noFormerSBAempSeparatedexpfield = page.locator('//*[@id="noFormerSBAempSeparatedExpl"]');
        this.noMemberSoleProprietorYes = page.locator("//label[@for='noMemberSoleProprietorYes']//span");
        this.noMemberSoleProprietorNo = page.locator("//label[contains(@for,'noMemberSoleProprietorNo')]//span");
        this.noMemberSoleProprietorexpsection = page.locator("//label[contains(@for,'noMemberSoleProprietorExpl')]");
        this.noMemberSoleProprietorexpfield = page.locator('//*[@id="noMemberSoleProprietorExpl"]');
        this.noGovEmpGS13Yes = page.locator("//label[@for='noGovEmpGS13Yes']//span");
        this.noGovEmpGS13No = page.locator("//label[contains(@for,'noGovEmpGS13No')]//span");
        this.noGovEmpGS13expsection = page.locator("//label[contains(@for,'noGovEmpGS13Expl')]");
        this.noGovEmpGS13expfield = page.locator('//*[@id="noGovEmpGS13Expl"]');
        this.noMemberSmallBuinessAdvisoryYes = page.locator("//label[@for='noMemberSmallBuinessAdvisoryYes']//span")
        this.noMemberSmallBuinessAdvisoryNo = page.locator("//label[contains(@for,'noMemberSmallBuinessAdvisoryNo')]//span");
        this.noMemberSmallBuinessAdvisoryexpsection = page.locator("//label[contains(@for,'noMemberSmallBuinessAdvisoryExpl')]");
        this.noMemberSmallBuinessAdvisoryexpfield = page.locator('//*[@id="noMemberSmallBuinessAdvisoryExpl"]');
        this.haveControlledBankruptcyProtectionYes = page.locator("//label[contains(@for,'haveControlledBankruptcyProtectionYes')]//span");
        this.haveControlledBankruptcyProtectionNo = page.locator("//label[contains(@for,'haveControlledBankruptcyProtectionNo')]//span");
        this.haveControlledBankruptcyProtectionexpsection = page.locator("//label[contains(@for,'haveControlledBankruptcyProtectionExpl')]");
        this.haveControlledBankruptcyProtectionexpfield = page.locator('//*[@id="haveControlledBankruptcyProtectionExpl"]');
        this.applicantsPayrollCalculationYes = page.locator("//label[contains(@for,'applicantsPayrollCalculationYes')]//span");
        this.applicantsPayrollCalculationNo = page.locator("//label[contains(@for,'applicantsPayrollCalculationNo')]//span");
        this.applicantsPayrollCalculationexpsection = page.locator("//label[contains(@for,'applicantsPayrollCalculationExpl')]");
        this.applicantsPayrollCalculationexpfield = page.locator('//*[@id="applicantsPayrollCalculationExpl"]');
        this.estimatedMonthlyPayrollYes = page.locator("//label[@for='estimatedMonthlyPayrollYes']//span");
        this.estimatedMonthlyPayrollNo = page.locator("//label[contains(@for,'estimatedMonthlyPayrollNo')]//span");
        this.estimatedMonthlyPayrollexpsection = page.locator("//label[contains(text(),'If Yes, Did you calculate your estimated monthly p')]");
        this.estimatedMonthlyPayrollexpfield = page.locator('//*[@id="estimatedMonthlyPayrollExpl"]');
        this.lossToTheGovernmentYes = page.locator("//label[@for='lossToTheGovernmentYes']//span");
        this.lossToTheGovernmentNo = page.locator("//label[contains(@for,'lossToTheGovernmentNo')]//span");
        this.lossToTheGovernmentexpsection = page.locator("//label[contains(@for,'lossToTheGovernmentExpl')]");
        this.lossToTheGovernmentexpfield = page.locator('//*[@id="lossToTheGovernmentExpl"]');
        this.businessControlLegalActionYes = page.locator("//label[@for='businessControlLegalActionYes']//span");
        this.businessControlLegalActionNo = page.locator("//label[contains(@for,'businessControlLegalActionNo')]//span");
        this.businessControlLegalActionexpsection = page.locator("//label[contains(@for,'businessControlLegalActionExpl')]");
        this.businessControlLegalActionexpfield = page.locator('//*[@id="businessControlLegalActionExpl"]');
        this.sbaEconomicInjuryYes = page.locator("//label[@for='sbaEconomicInjuryYes']//span");
        this.sbaEconomicInjuryNo = page.locator("//label[contains(@for,'sbaEconomicInjuryNo')]//span");
        this.sbaEconomicInjuryexpsection = page.locator("//label[contains(@for,'sbaEconomicInjuryExpl')]");
        this.sbaEconomicInjuryexpfield = page.locator('//*[@id="sbaEconomicInjuryExpl"]');
        this.sbaEconomicInjuryLoanAmt = page.locator('//*[@id="sbaEconomicInjuryLoanAmt"]');
        this.haveOwnershipAffiliateYes = page.locator("//label[@for='haveOwnershipAffiliateYes']//span");
        this.haveOwnershipAffiliateNo = page.locator("//label[@for='haveOwnershipAffiliateNo']//span");
        this.haveOwnershipAffiliateexpsection = page.locator("//label[@for='haveOwnershipAffiliateExpl']");
        this.haveOwnershipAffiliateexpfield = page.locator('//*[@id="haveOwnershipAffiliateExpl"]');
        this.removebusinessInfo = page.locator("//i[@title='Click to remove fields']");
        this.addOtherBusinessInfo = page.locator("//i[@data-original-title='Click to add more fields']");
        this.companyname = page.locator('//*[@id="companyName_2"]');
        this.taxid = page.locator('//*[@id="taxId_2"]');
        this.legalowners = page.locator('//*[@id="legalOwners_2"]');
        this.idofemp = page.locator('//*[@id="noOfEmployees_2"]');
        this.yrinbusiness = page.locator('//*[@id="yearsInBusiness_2"]');
        this.appliedforloan = page.locator('//*[@id="sba_2"]/div/div[8]/div/div/label[1]/span');
        this.decsription = page.locator('//*[@id="sbaDesc_2"]');
        this.appliedforloanYes = page.locator('//*[@id="sba_2"]/div/div[8]/div/div/label[1]');
        this.appliedforloanNo = page.locator('//*[@id="sba_2"]/div/div[8]/div/div/label[2]');
        this.w2empfield = page.locator('//*[@id="noOfw2Employee"]');
        this.w2issuefield = page.locator('//*[@id="noOfw2sIssue"]');
        this.companystarted = page.locator('//*[@id="companyStarted"]');
        this.employeeFamilyOwnerfield = page.locator('//*[@id="employeeFamilyOwner"]');
        this.otherbusiness = page.locator('//*[@id="startedOtherBusiness"]');
        this.otherexistbusiness = page.locator('//*[@id="otherExistingBusiness"]');
        this.sbaLoanDelinquentYes = page.locator("//label[contains(@for,'anyFinancingDelinquentYes')]//span");
        this.sbaLoanDelinquentNo = page.locator("//label[contains(@for,'anyFinancingDelinquentNo')]//span");
        this.sbaLoanDefaultLossYes = page.locator("//label[contains(@for,'anyFinancingDefaultYes')]//span");
        this.sbaLoanDefaultLossNo = page.locator("//label[contains(@for,'anyFinancingDefaultNo')]//span");
    }

    async fillPPPDrawAmounts(pppAmounts: Record<"draw1" | "draw2", string | number>): Promise<void> {
        const drawMap: Record<string, Locator> = {
            draw1: this.ppp_draw1_input,
            draw2: this.ppp_draw2_input
        };

        for (const [draw, amount] of Object.entries(pppAmounts)) {
            const locator = drawMap[draw];
            if (!locator) {
                console.warn(`Missing locator for: ${draw}`);
                continue;
            }

            await this.fillAndValidate(locator, String(amount));
        }
    }

  async fillPPPDrawForgiveness(
    draw1: "Yes" | "No",
    draw2: "Yes" | "No"
    ): Promise<void> {
        const app = new AppManager(this.page);
        await app.utilities.scrollToElement2(this.page, this.pppdrawforgive);
        //await this.page.waitForTimeout(2000);
        if (draw1 === "Yes") {
            await this.ppp_draw1_forgiveness_yes.click();
            }
        else if (draw1 === "No"){
            await this.ppp_draw1_forgiveness_no.click();
            }  

        if (draw2 === "Yes"){
            await this.ppp_draw2_forgiveness_yes.click();
        }
        else if (draw2 === "No"){
            await this.ppp_draw2_forgiveness_no.click();
        }

        
    }

    async selectemployeeFamilyOwner(value: string) {
        await expect(this.employeeFamilyOwnerfield).toBeEnabled();
        await this.employeeFamilyOwnerfield.click();
        await this.fillAndValidate(this.employeeFamilyOwnerfield, value);
    }
    async selectstartedotherbusiness(value: string) {
        await expect(this.otherbusiness).toBeEnabled();
        await this.otherbusiness.click();
        await this.fillAndValidate(this.otherbusiness, value);
    }
    async selectotherExistingBusiness(value: string) {
        await expect(this.otherexistbusiness).toBeEnabled();
        await this.otherexistbusiness.click();
        await this.fillAndValidate(this.otherexistbusiness, value);
    }
    async checksbaquestionsvisibility() {
        //await this.sbaquestionscard.scrollIntoViewIfNeeded();
        await expect(this.sbaquestionscard).toBeVisible({ timeout: 5000 });
    }
    async selectAverageMonthlyPayrollOfAllEmployees(amount: number) {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.monthlyPayroll);
        await expect(this.monthlyPayroll).toBeVisible();
        await this.monthlyPayroll.clear();
        await this.monthlyPayroll.fill(amount.toString());
        await expect(this.monthlyPayroll).toHaveValue(amount.toString());
    }
    async selectPurposeOfLoan() {
        const app = new AppManager(this.page);
        // Define the values directly inside the function
        const purposes = [
            "Payroll",
            "Rent/Mortgage Interest",
            "Utilities",
            "Other"
        ];

        for (const purpose of purposes) {
            const selectpurpose = this.page.locator(`//label[normalize-space()='${purpose}']//span`);
            await app.utilities.scrollToElement2(this.page, selectpurpose);
            //app.utilities.scrollToElement2(this.page, this.purposechechbox);
            // Deselect if already selected
            if (await selectpurpose.isChecked()) {
                await selectpurpose.click();
            }

            // Select the checkbox
            await selectpurpose.click();
        }
    }
    async selectDetailedExplanation(explain: string) {
        const explainationfeild = this.page.locator('//*[@id="purposeOfTheLoanOtherExpl"]')
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, explainationfeild);
        // Ensure field is visible and enabled
        await expect(explainationfeild).toBeVisible();
        await expect(explainationfeild).toBeEnabled();
        // Clear existing value
        await explainationfeild.click();
        await explainationfeild.press('Control+A');
        await explainationfeild.press('Backspace');
        await explainationfeild.fill(explain);
    }
    async selectcriminalinformation(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination1?: string;
        }
    ): Promise<void> {
        if (option === "Yes") {
            // Click YES
            await this.criminalinfoyes.click();
            await this.criminaldetailexplainationsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination1) {
                await this.criminaldetailexplainationfeild.click();
                await this.criminaldetailexplainationfeild.press('Control+A');
                await this.criminaldetailexplainationfeild.press('Backspace');
                await this.fillAndValidate(this.criminaldetailexplainationfeild, extraData.detailedexplaination1);
            }
        } else {
            await this.criminalinfono.click();
        }

    }
    async fillAndValidate(locator: Locator, value: string) {
        await locator.fill(value);
        await expect(locator).toHaveValue(value);
    }
    async selecthaveyouarrested(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination2?: string;
        }
    ): Promise<void> {
        if (option === "Yes") {
            // Click YES
            await this.haveyouarrestedyes.click();
            await this.arresteddetailexplainationsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination2) {
                await this.arresteddetailexplainationfeild.click();
                await this.arresteddetailexplainationfeild.press('Control+A');
                await this.arresteddetailexplainationfeild.press('Backspace');
                await this.fillAndValidate(this.arresteddetailexplainationfeild, extraData.detailedexplaination2);
            }
        } else {
            await this.haveyouarrestedno.click();
        }

    }
    async selectcriminaloffence(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination3?: string;
        }
    ): Promise<void> {
        if (option === "Yes") {
            // Click YES
            await this.criminaloffenceyes.click();
            await this.criminaloffencedetailexplainationsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination3) {
                await this.criminaloffencedetailexplainationfield.click();
                await this.criminaloffencedetailexplainationfield.press('Control+A');
                await this.criminaloffencedetailexplainationfield.press('Backspace');
                await this.fillAndValidate(this.criminaloffencedetailexplainationfield, extraData.detailedexplaination3);
            }
        } else {
            await this.criminaloffenceno.click();
        }
    }
    async selectCertifiedDevelopmentCompany(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination4?: string;
        }
    ): Promise<void> {
        if (option === "Yes") {
            // Click YES
            await this.certifieddevelopmentyes.click();
            await this.certifieddevelopmentexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination4) {
                await this.certifieddevelopmentexpfield.click();
                await this.certifieddevelopmentexpfield.press('Control+A');
                await this.certifieddevelopmentexpfield.press('Backspace');
                await this.fillAndValidate(this.certifieddevelopmentexpfield, extraData.detailedexplaination4);
            }
        } else {
            await this.certifieddevelopmentno.click();
        }

    }
    async selectSBAFranchiseDirectory(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination5?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.sBAFranchiseDirectoryexpfield);
        if (option === "Yes") {
            // Click YES
            await this.sBAFranchiseDirectoryyes.click();
            await this.sBAFranchiseDirectoryexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination5) {
                await this.sBAFranchiseDirectoryexpfield.click();
                await this.sBAFranchiseDirectoryexpfield.press('Control+A');
                await this.sBAFranchiseDirectoryexpfield.press('Backspace');
                await this.fillAndValidate(this.sBAFranchiseDirectoryexpfield, extraData.detailedexplaination5);
            }
        } else {
            await this.sBAFranchiseDirectoryno.click();
        }

    }
    async selectSmallBusinessApplicant(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination6?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.smallBusinessApplicantexpfield);
        if (option === "Yes") {
            // Click YES
            await this.smallBusinessApplicantyes.click();
            await this.smallBusinessApplicantexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination6) {
                await this.smallBusinessApplicantexpfield.click();
                await this.smallBusinessApplicantexpfield.press('Control+A');
                await this.smallBusinessApplicantexpfield.press('Backspace');
                await this.fillAndValidate(this.smallBusinessApplicantexpfield, extraData.detailedexplaination6);
            }
        } else {
            await this.smallBusinessApplicantno.click();
        }

    }
    async selectSmallBusinessApplicantBankruptcy(
        option: "Yes" | "No", optionToSelect: string,
        extraData?: {
            detailedexplaination7?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.smallBusinessApplicantBankruptcyexpfield);
        if (option === "Yes") {
            // Click YES
            await this.smallBusinessApplicantBankruptcyyes.click();

            if (await this.closeapplicantbankruptcy.isVisible()) {
                await this.closeapplicantbankruptcy.click();
            }
                await this.page.waitForTimeout(2000);
                const dropdown = this.page.locator('//*[@id="businessBankruptcy_chosen"]/a/span');
                await dropdown.click();
                const dropdownOptions = this.page.locator("ul.chosen-results li");
                const option = dropdownOptions.filter({ hasText: optionToSelect });
                await option.click();
            
            await this.smallBusinessApplicantBankruptcyexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination7) {
                await this.smallBusinessApplicantBankruptcyexpfield.click();
                await this.smallBusinessApplicantBankruptcyexpfield.press('Control+A');
                await this.smallBusinessApplicantBankruptcyexpfield.press('Backspace');
                await this.fillAndValidate(this.smallBusinessApplicantBankruptcyexpfield, extraData.detailedexplaination7);
            }
        } else {
            await this.smallBusinessApplicantBankruptcyno.click();
        }

    }
    async selectsmallBusinessApplicantLegal(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination8?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.smallBusinessApplicantLegalexpfield);
        if (option === "Yes") {
            // Click YES
            await this.smallBusinessApplicantLegalYes.click();
            await this.smallBusinessApplicantLegalexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination8) {
                await this.smallBusinessApplicantLegalexpfield.click();
                await this.smallBusinessApplicantLegalexpfield.press('Control+A');
                await this.smallBusinessApplicantLegalexpfield.press('Backspace');
                await this.fillAndValidate(this.smallBusinessApplicantLegalexpfield, extraData.detailedexplaination8);
            }
        } else {
            await this.smallBusinessApplicantLegalNo.click();
        }

    }
    async selectdebarredSuspendedFederal(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination9?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.debarredSuspendedFederalexpfield);
        if (option === "Yes") {
            // Click YES
            await this.debarredSuspendedFederalYes.click();
            await this.debarredSuspendedFederalexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination9) {
                await this.debarredSuspendedFederalexpfield.click();
                await this.debarredSuspendedFederalexpfield.press('Control+A');
                await this.debarredSuspendedFederalexpfield.press('Backspace');
                await this.fillAndValidate(this.debarredSuspendedFederalexpfield, extraData.detailedexplaination9);
            }
        } else {
            await this.debarredSuspendedFederalNo.click();
        }

    }
    async selectchildSupportEnforcementServices(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination10?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.childSupportEnforcementServicesexpfield);
        if (option === "Yes") {
            // Click YES
            await this.childSupportEnforcementServicesYes.click();
            await this.childSupportEnforcementServicesexpsectiion.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination10) {
                await this.childSupportEnforcementServicesexpfield.click();
                await this.childSupportEnforcementServicesexpfield.press('Control+A');
                await this.childSupportEnforcementServicesexpfield.press('Backspace');
                await this.fillAndValidate(this.childSupportEnforcementServicesexpfield, extraData.detailedexplaination10);
            }
        } else {
            await this.childSupportEnforcementServicesNo.click();
        }

    }
   async selectSbaLoanOption(
    option: "Yes" | "No",
    extraData?: {
        delinquent?: "Yes" | "No";
        delinquentExplanation?: string;
        defaultLoss?: "Yes" | "No";
        defaultLossExplanation?: string;
    }
    ): Promise<void> {
    const app = new AppManager(this.page);
    app.utilities.scrollToElement2(this.page, this.sbaLoanSectionField);

    if (option === "Yes") {
        // Click YES for SBA loan main question
        await this.sbaLoanYes.click();

        // Wait for the dependent sections to appear
        await this.sbaLoanDelinquentSection.waitFor({ state: "visible" });
        await this.sbaLoanDefaultLossSection.waitFor({ state: "visible" });

        // Handle delinquent section
        if (extraData?.delinquent === "Yes") {
            await this.sbaLoanDelinquentYes.click();
            await this.sbaLoanDelinquentExplanationField.waitFor({ state: "visible" });

            if (extraData.delinquentExplanation) {
                await this.sbaLoanDelinquentExplanationField.click();
                await this.sbaLoanDelinquentExplanationField.press('Control+A');
                await this.sbaLoanDelinquentExplanationField.press('Backspace');
                await this.fillAndValidate(this.sbaLoanDelinquentExplanationField, extraData.delinquentExplanation);
            }
        } else if (extraData?.delinquent === "No") {
            await this.sbaLoanDelinquentNo.click();
        }

        // Handle default loss section
        if (extraData?.defaultLoss === "Yes") {
            await this.sbaLoanDefaultLossYes.click();
            await this.sbaLoanDefaultLossExplanationField.waitFor({ state: "visible" });

            if (extraData.defaultLossExplanation) {
                await this.sbaLoanDefaultLossExplanationField.click();
                await this.sbaLoanDefaultLossExplanationField.press('Control+A');
                await this.sbaLoanDefaultLossExplanationField.press('Backspace');
                await this.fillAndValidate(this.sbaLoanDefaultLossExplanationField, extraData.defaultLossExplanation);
            }
        } else if (extraData?.defaultLoss === "No") {
            await this.sbaLoanDefaultLossNo.click();
        }

    } else {
        // Click NO for SBA loan main question
        await this.sbaLoanNo.click();
    }
    }

    async selectsmallBusinessApplicantExport(
        option: "Yes" | "No",
        extraData?: {
            amount?: number;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.smallBusinessApplicantExportamountfield);
        if (option === "Yes") {
            // Click YES
            await this.smallBusinessApplicantExportYes.click();
            await this.smallBusinessApplicantExportamountsec.waitFor({
                state: "visible",
            });
            if (extraData?.amount) {
                await this.smallBusinessApplicantExportamountfield.click();
                await this.smallBusinessApplicantExportamountfield.press('Control+A');
                await this.smallBusinessApplicantExportamountfield.press('Backspace');
                await this.fillAndValidate(this.smallBusinessApplicantExportamountfield, extraData.amount.toString());
            }
        } else {
            await this.smallBusinessApplicantExportNo.click();
        }

    }
    async selectsmallBusinessApplicantPacker(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination11?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.smallBusinessApplicantPackerexpfield);
        if (option === "Yes") {
            // Click YES
            await this.smallBusinessApplicantPackerYes.click();
            await this.smallBusinessApplicantPackerexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination11) {
                await this.smallBusinessApplicantPackerexpfield.click();
                await this.smallBusinessApplicantPackerexpfield.press('Control+A');
                await this.smallBusinessApplicantPackerexpfield.press('Backspace');
                await this.fillAndValidate(this.smallBusinessApplicantPackerexpfield, extraData.detailedexplaination11);
            }
        } else {
            await this.smallBusinessApplicantPackerNo.click();
        }

    }
    async selectsmallBusinessApplicantRevenue(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination12?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.smallBusinessApplicantRevenuefield);
        if (option === "Yes") {
            // Click YES
            await this.smallBusinessApplicantRevenueYes.click();
            await this.smallBusinessApplicantRevenueexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination12) {
                await this.smallBusinessApplicantRevenuefield.click();
                await this.smallBusinessApplicantRevenuefield.press('Control+A');
                await this.smallBusinessApplicantRevenuefield.press('Backspace');
                await this.fillAndValidate(this.smallBusinessApplicantRevenuefield, extraData.detailedexplaination12);
            }
        } else {
            await this.smallBusinessApplicantRevenueNo.click();
        }

    }
    async selectnoSBAempHouseHoldMember(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination13?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.noSBAempHouseHoldMemberexpfield);
        if (option === "Yes") {
            // Click YES
            await this.noSBAempHouseHoldMemberYes.click();
            await this.noSBAempHouseHoldMemberexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination13) {
                await this.noSBAempHouseHoldMemberexpfield.click();
                await this.noSBAempHouseHoldMemberexpfield.press('Control+A');
                await this.noSBAempHouseHoldMemberexpfield.press('Backspace');
                await this.fillAndValidate(this.noSBAempHouseHoldMemberexpfield, extraData.detailedexplaination13);
            }
        } else {
            await this.noSBAempHouseHoldMemberNo.click();
        }

    }
    async selectnoFormerSBAempSeparated(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination14?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.noFormerSBAempSeparatedexpfield);
        if (option === "Yes") {
            // Click YES
            await this.noFormerSBAempSeparatedYes.click();
            await this.noFormerSBAempSeparatedexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination14) {
                await this.noFormerSBAempSeparatedexpfield.click();
                await this.noFormerSBAempSeparatedexpfield.press('Control+A');
                await this.noFormerSBAempSeparatedexpfield.press('Backspace');
                await this.fillAndValidate(this.noFormerSBAempSeparatedexpfield, extraData.detailedexplaination14);
            }
        } else {
            await this.noFormerSBAempSeparatedNo.click();
        }

    }
    async selectnoMemberSoleProprietor(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination15?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.noMemberSoleProprietorexpfield);
        if (option === "Yes") {
            // Click YES
            await this.noMemberSoleProprietorYes.click();
            await this.noMemberSoleProprietorexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination15) {
                await this.noMemberSoleProprietorexpfield.click();
                await this.noMemberSoleProprietorexpfield.press('Control+A');
                await this.noMemberSoleProprietorexpfield.press('Backspace');
                await this.fillAndValidate(this.noMemberSoleProprietorexpfield, extraData.detailedexplaination15);
            }
        } else {
            await this.noMemberSoleProprietorNo.click();
        }

    }
    async selectnoGovEmpGS13(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination16?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.noGovEmpGS13expfield);
        if (option === "Yes") {
            // Click YES
            await this.noGovEmpGS13Yes.click();
            await this.noGovEmpGS13expsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination16) {
                await this.noGovEmpGS13expfield.click();
                await this.noGovEmpGS13expfield.press('Control+A');
                await this.noGovEmpGS13expfield.press('Backspace');
                await this.fillAndValidate(this.noGovEmpGS13expfield, extraData.detailedexplaination16);
            }
        } else {
            await this.noGovEmpGS13No.click();
        }
    }
    async selectnoMemberSmallBuinessAdvisory(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination17?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.noMemberSmallBuinessAdvisoryexpfield);
        if (option === "Yes") {
            // Click YES
            await this.noMemberSmallBuinessAdvisoryYes.click();
            await this.noMemberSmallBuinessAdvisoryexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination17) {
                await this.noMemberSmallBuinessAdvisoryexpfield.click();
                await this.noMemberSmallBuinessAdvisoryexpfield.press('Control+A');
                await this.noMemberSmallBuinessAdvisoryexpfield.press('Backspace');
                await this.fillAndValidate(this.noMemberSmallBuinessAdvisoryexpfield, extraData.detailedexplaination17);
            }
        } else {
            await this.noMemberSmallBuinessAdvisoryNo.click();
        }

    }
    async selecthaveControlledBankruptcyProtection(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination18?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.haveControlledBankruptcyProtectionexpfield);
        if (option === "Yes") {
            // Click YES
            await this.haveControlledBankruptcyProtectionYes.click();
            await this.haveControlledBankruptcyProtectionexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination18) {
                await this.haveControlledBankruptcyProtectionexpfield.click();
                await this.haveControlledBankruptcyProtectionexpfield.press('Control+A');
                await this.haveControlledBankruptcyProtectionexpfield.press('Backspace');
                await this.fillAndValidate(this.haveControlledBankruptcyProtectionexpfield, extraData.detailedexplaination18);
            }
        } else {
            await this.haveControlledBankruptcyProtectionNo.click();
        }

    }
    async selectapplicantsPayrollCalculation(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination19?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.applicantsPayrollCalculationexpfield);
        if (option === "Yes") {
            // Click YES
            await this.applicantsPayrollCalculationYes.click();
            await this.applicantsPayrollCalculationexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination19) {
                await this.applicantsPayrollCalculationexpfield.click();
                await this.applicantsPayrollCalculationexpfield.press('Control+A');
                await this.applicantsPayrollCalculationexpfield.press('Backspace');
                await this.fillAndValidate(this.applicantsPayrollCalculationexpfield, extraData.detailedexplaination19);
            }
        } else {
            await this.applicantsPayrollCalculationNo.click();
        }
    }
    async selectestimatedMonthlyPayroll(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination20?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.estimatedMonthlyPayrollexpfield);
        if (option === "Yes") {
            // Click YES
            await this.estimatedMonthlyPayrollYes.click();
            await this.estimatedMonthlyPayrollexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination20) {
                await this.estimatedMonthlyPayrollexpfield.click();
                await this.estimatedMonthlyPayrollexpfield.press('Control+A');
                await this.estimatedMonthlyPayrollexpfield.press('Backspace');
                await this.fillAndValidate(this.estimatedMonthlyPayrollexpfield, extraData.detailedexplaination20);
            }
        } else {
            await this.estimatedMonthlyPayrollNo.click();
        }
    }
    async selectlossToTheGovernment(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination21?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.lossToTheGovernmentexpfield);
        if (option === "Yes") {
            // Click YES
            await this.lossToTheGovernmentYes.click();
            await this.lossToTheGovernmentexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination21) {
                await this.lossToTheGovernmentexpfield.click();
                await this.lossToTheGovernmentexpfield.press('Control+A');
                await this.lossToTheGovernmentexpfield.press('Backspace');
                await this.fillAndValidate(this.lossToTheGovernmentexpfield, extraData.detailedexplaination21);
            }
        } else {
            await this.lossToTheGovernmentNo.click();
        }
    }
    async selectbusinessControlLegalAction(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination22?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.businessControlLegalActionexpfield);
        if (option === "Yes") {
            // Click YES
            await this.businessControlLegalActionYes.click();
            await this.businessControlLegalActionexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.detailedexplaination22) {
                await this.businessControlLegalActionexpfield.click();
                await this.businessControlLegalActionexpfield.press('Control+A');
                await this.businessControlLegalActionexpfield.press('Backspace');
                await this.fillAndValidate(this.businessControlLegalActionexpfield, extraData.detailedexplaination22);
            }
        } else {
            await this.businessControlLegalActionNo.click();
        }
    }
    async selectw2issuesin2020(count: number) {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.w2issuefield);

        await this.w2issuefield.click();
        await this.w2issuefield.clear();
        await this.fillAndValidate(this.w2issuefield, count.toString());
    }
    async selectw2Employeescount(count: number) {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.w2empfield);

        await this.w2empfield.click();
        await this.w2empfield.clear();
        await this.fillAndValidate(this.w2empfield, count.toString());
    }
    async selectWhenWasCompanyStarted(value: string) {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.companystarted);
        await this.companystarted.selectOption({ label: value });

        await expect(this.companystarted).toContainText(value);
    }
    async selectHaveOwnershipAffiliate(
        option: "Yes" | "No",
        extraData?: {
            detailedexplaination24?: string;
            businessinfo?: {
                companyname?: string;
                taxid?: number;
                legalowners?: number;
                idofemp?: number;
                yrinbusiness?: number;
                appliedforloan?: string;
                description?: string;
            };
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.haveOwnershipAffiliateexpfield);

        if (option === "Yes") {
            await this.haveOwnershipAffiliateYes.click();
            await this.haveOwnershipAffiliateexpsection.waitFor({ state: "visible" });
            await this.page.waitForTimeout(2000);

            // Handle business info section
            if (extraData?.businessinfo) {
                const isRemoveVisible = await this.removebusinessInfo.isVisible()
                if (isRemoveVisible) {
                    await this.removebusinessInfo.click();
                    await expect(this.companyname).toBeHidden();
                }

                await this.addOtherBusinessInfo.click();

                // Fill fields (convert numbers to strings where necessary)
                if (extraData.businessinfo.companyname) {
                    await this.fillAndValidate(this.companyname, extraData.businessinfo.companyname);
                }
                if (extraData.businessinfo.taxid !== undefined) {
                    await this.fillAndValidate(this.taxid, extraData.businessinfo.taxid.toString());
                }
                if (extraData.businessinfo.legalowners !== undefined) {
                    await this.fillAndValidate(this.legalowners, extraData.businessinfo.legalowners.toString());
                }
                if (extraData.businessinfo.idofemp !== undefined) {
                    await this.fillAndValidate(this.idofemp, extraData.businessinfo.idofemp.toString());
                }
                if (extraData.businessinfo.yrinbusiness !== undefined) {
                    await this.fillAndValidate(this.yrinbusiness, extraData.businessinfo.yrinbusiness.toString());
                }
                if (extraData.businessinfo.appliedforloan) {
                    if (extraData.businessinfo.appliedforloan === "Yes") {
                        await this.appliedforloanYes.click();
                    } else {
                        await this.appliedforloanNo.click();
                    }
                }
                if (extraData.businessinfo.description) {
                    await this.fillAndValidate(this.decsription, extraData.businessinfo.description);
                }
            }

            if (extraData?.detailedexplaination24) {
                await this.haveOwnershipAffiliateexpfield.click();
                await this.haveOwnershipAffiliateexpfield.press('Control+A');
                await this.haveOwnershipAffiliateexpfield.press('Backspace');
                await this.fillAndValidate(this.haveOwnershipAffiliateexpfield, extraData.detailedexplaination24);

            }
        } else {

            await this.haveOwnershipAffiliateNo.click();
        }

    }
    async selectsbaEconomicInjury(
        option: "Yes" | "No",
        extraData?: {
            loanamount?: number,
            detailedexplaination23?: string;
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.sbaEconomicInjuryexpfield);
        if (option === "Yes") {
            // Click YES
            await this.sbaEconomicInjuryYes.click();
            await this.sbaEconomicInjuryexpsection.waitFor({
                state: "visible",
            });
            if (extraData?.loanamount) {
                await this.sbaEconomicInjuryLoanAmt.click();
                await this.sbaEconomicInjuryLoanAmt.press('Control+A');
                await this.sbaEconomicInjuryLoanAmt.press('Backspace');
                await this.fillAndValidate(this.sbaEconomicInjuryLoanAmt, extraData.loanamount.toString());
            }
            if (extraData?.detailedexplaination23) {
                await this.sbaEconomicInjuryexpfield.click();
                await this.sbaEconomicInjuryexpfield.press('Control+A');
                await this.sbaEconomicInjuryexpfield.press('Backspace');
                await this.fillAndValidate(this.sbaEconomicInjuryexpfield, extraData.detailedexplaination23);
            }
        } else {
            await this.sbaEconomicInjuryNo.click();
        }

    }
    async fillGrossReceipts1(grossReceipts1: Record<string, string>): Promise<void> {
        const quarterMap: Record<string, Locator> = {
            Q1_2019: this.q1_2019_input,
            Q2_2019: this.q2_2019_input,
            Q3_2019: this.q3_2019_input,
            Q4_2019: this.q4_2019_input,
            Q1_2020: this.q1_2020_input,
            Q2_2020: this.q2_2020_input,
            Q3_2020: this.q3_2020_input,
            Q4_2020: this.q4_2020_input,
            Q1_2021: this.q1_2021_input,
            Q2_2021: this.q2_2021_input,
            Q3_2021: this.q3_2021_input,
            Q4_2021: this.q4_2021_input
        };

        for (const [quarter, amount] of Object.entries(grossReceipts1)) {
            const locator = quarterMap[quarter];
            if (!locator) {
                console.warn(`Missing locator for: ${quarter}`);
                continue;
            }

            await this.fillAndValidate(locator, amount);
        }
    }
    async fillGrossReceipts2(grossReceipts2: Record<string, string>): Promise<void> {
        const quarterMap: Record<string, Locator> = {
            Q1_2019: this.q1_2019_input2,
            Q2_2019: this.q2_2019_input2,
            Q3_2019: this.q3_2019_input2,
            Q4_2019: this.q4_2019_input2,
            Q1_2020: this.q1_2020_input2,
            Q2_2020: this.q2_2020_input2,
            Q3_2020: this.q3_2020_input2,
            Q4_2020: this.q4_2020_input2,
            Q1_2021: this.q1_2021_input2,
            Q2_2021: this.q2_2021_input2,
            Q3_2021: this.q3_2021_input2,
            Q4_2021: this.q4_2021_input2
        };

        for (const [quarter, amount] of Object.entries(grossReceipts2)) {
            const locator = quarterMap[quarter];
            if (!locator) {
                console.warn(`Missing locator for: ${quarter}`);
                continue;
            }

            await this.fillAndValidate(locator, amount);
        }
    }
    async selectGovOrderSuspensionStatus(
        option: "No" | "Partial Suspension" | "Full Suspension",
        extraData?: {
            impactedExplanation?: string;
            affectedQuarters?: string
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.govOrderSuspensionField);

        if (option === "No") {
            await this.govOrderSuspensionNo.click();
        } else {
            // Handle Partial or Full Suspension
            if (option === "Partial Suspension") {
                await this.govOrderSuspensionPartial.click();
            } else if (option === "Full Suspension") {
                await this.govOrderSuspensionFull.click();
            }

            // Wait for dependent fields to appear
            await this.govOrderImpactedExplanation.waitFor({ state: "visible" });
            await this.page.waitForTimeout(1000);

            if (extraData?.impactedExplanation) {
                await this.govOrderImpactedExplanation.click();
                await this.govOrderImpactedExplanation.press("Control+A");
                await this.govOrderImpactedExplanation.press("Backspace");
                await this.fillAndValidate(this.govOrderImpactedExplanation, extraData.impactedExplanation);
            }

            if (extraData?.affectedQuarters) {
                await this.govOrderAffectedQuarters.click();
                const optionToSelect = this.listValueOfInputFields.filter({ hasText: extraData.affectedQuarters });
                await optionToSelect.waitFor({ state: 'visible' });
                await optionToSelect.click();
            }
        }
    }
    async selectVendorSuspensionStatus(
        option: "No" | "Partial Suspension" | "Full Suspension" | "Not Sure",
        extraData?: {
            vendorImpactDetails?: {
                affectedQuarters?: string;
                impactExplanation?: string;
            };
        }
    ): Promise<void> {
        const app = new AppManager(this.page);
        app.utilities.scrollToElement2(this.page, this.vendorSuspensionNoOption); // scroll to field

        switch (option) {
            case "No":
                await this.vendorSuspensionNoOption.click();
                break;

            case "Not Sure":
                await this.vendorSuspensionNotSureOption.click();
                break;

            case "Partial Suspension":
            case "Full Suspension":
                // Shared logic for both suspension types
                if (option === "Partial Suspension") {
                    await this.vendorSuspensionPartialOption.click();
                } else {
                    await this.vendorSuspensionFullOption.click();
                }

                // Wait for dependent fields to appear
                await this.vendorImpactExplanationField.waitFor({ state: "visible" });

                // Fill impact explanation
                if (extraData?.vendorImpactDetails?.impactExplanation) {
                    await this.fillAndValidate(this.vendorImpactExplanationField, extraData.vendorImpactDetails.impactExplanation);
                }

                // Fill affected quarters
                if (extraData?.vendorImpactDetails?.affectedQuarters) {
                    await this.vendorAffectedQuartersField.click();
                    const optionToSelect = this.listValueOfInputFields.filter({ hasText: extraData.vendorImpactDetails.affectedQuarters }).nth(1);
                    await optionToSelect.waitFor({ state: 'visible' });
                    await optionToSelect.click();
                }
                break;
        }

    }
    async havePPPLoanForgiven(ForgivenAmounts: Record<"draw1" | "draw2", string | number>): Promise<void> {
        const drawMap: Record<string, Locator> = {
            draw1: this.havePPPLoanForgivenessDraw1,
            draw2: this.havePPPLoanForgivenessDraw2
        };

        for (const [draw, amount] of Object.entries(ForgivenAmounts)) {
            const locator = drawMap[draw];
            if (!locator) {
                console.warn(`Missing locator for: ${draw}`);
                continue;
            }

            await this.fillAndValidate(locator, String(amount));
        }
    }
    async selectpayrollServiceProvider(value: string) {
        await expect(this.payrollservice).toBeEnabled();
        await this.payrollservice.click();
        await this.fillAndValidate(this.payrollservice, value);
    }
    async selectEmpRetentionCre(
        option: "Yes" | "No",
        extraData?: {
            whichquarterandcompany?: string;
        }
    ): Promise<void> {
        // const app = new AppManager(this.page);
        // app.utilities.scrollToElement2(this.page, this.businessControlLegalActionexpfield);
        if (option === "Yes") {
            // Click YES
            await this.empretentionYes.click();
            await this.quarterandcompanysec.waitFor({
                state: "visible",
            });
            if (extraData?.whichquarterandcompany) {
                await this.quarterandcompanysec.click();
                await this.quarterandcompanysec.press('Control+A');
                await this.quarterandcompanysec.press('Backspace');
                await this.fillAndValidate(this.quarterandcompanysec, extraData.whichquarterandcompany);
            }
        } else {
            await this.empretentionNo.click();
        }

    }
    async selectquarterExperiencedLoss2020(value: string) {
        await expect(this.quarterExpLoss2020).toBeEnabled();
        await this.quarterExpLoss2020.click();
        const optionToSelect = this.listValueOfInputFields.filter({ hasText: value }).nth(2);
        await optionToSelect.waitFor({ state: 'visible' });
        await optionToSelect.click();
    }
    async selectquarterExperiencedLoss2021(value: string) {
        await expect(this.quarterExpLoss2021).toBeEnabled();
        await this.quarterExpLoss2021.click();
        const optionToSelect = this.listValueOfInputFields.filter({ hasText: value }).nth(2);
        await optionToSelect.waitFor({ state: 'visible' });
        await optionToSelect.click();
    }
}
