import { expect, Locator, <PERSON> } from "@playwright/test";

export class AdditionalGuarantors {
  readonly page: Page;

  // Section Radios
  readonly additionalGuarantorsYes: Locator;
  readonly additionalGuarantorsNo: Locator;

  // Dynamic Guarantor fields (first guarantor row by default)
  readonly guarantorFName: Locator;
  readonly guarantorMName: Locator;
  readonly guarantorLName: Locator;
  readonly guarantorPhone: Locator;
  readonly guarantorCell: Locator;
  readonly guarantorSSN: Locator;
  readonly guarantorDOB: Locator;
  readonly guarantorStatus: Locator;
  readonly guarantorAddress: Locator;
  readonly guarantorCity: Locator;
  readonly guarantorState: Locator;
  readonly guarantorZip: Locator;
  readonly guarantorEmail: Locator;
  readonly guarantorCreditScore: Locator;
  readonly guarantorCitizenshipUS: Locator;
  readonly guarantorCitizenshipPerm: Locator;
  readonly guarantorCitizenshipNonPerm: Locator;
  readonly guarantorCitizenshipForeign: Locator;
  readonly guarantorNote: Locator;

  // Add/remove guarantor
  readonly addGuarantorBtn: Locator;
  readonly removeGuarantorBtn: Locator;

  // General Notes
  readonly guarantorNotes: Locator;
  readonly borrowerNotes: Locator;

  constructor(page: Page) {
    this.page = page;

    // Radios
    this.additionalGuarantorsYes = page.locator("#isAdditionalGuarantorsYes");
    this.additionalGuarantorsNo = page.locator("#isAdditionalGuarantorsNo");

    // Fields
    this.guarantorFName = page.locator("#guarantorFName");
    this.guarantorMName = page.locator("#guarantorMName");
    this.guarantorLName = page.locator("#guarantorLName");
    this.guarantorPhone = page.locator("#guarantorPhone");
    this.guarantorCell = page.locator("#guarantorCellNumber");
    this.guarantorSSN = page.locator("#guarantorSSN");
    this.guarantorDOB = page.locator("#guarantorDOB");
    this.guarantorStatus = page.locator("#guarantorStatus");
    this.guarantorAddress = page.locator("#guarantorAddress");
    this.guarantorCity = page.locator("#guarantorCity");
    this.guarantorState = page.locator("#guarantorState");
    this.guarantorZip = page.locator("#guarantorZip");
    this.guarantorEmail = page.locator("#guarantorEmail");
    this.guarantorCreditScore = page.locator("#guarantorCreditScore");

    // Citizenship
    this.guarantorCitizenshipUS = page.locator("#guarantorCitizenshipus_0");
    this.guarantorCitizenshipPerm = page.locator("#guarantorCitizenshipperm_0");
    this.guarantorCitizenshipNonPerm = page.locator(
      "#guarantorCitizenshipnon_0"
    );
    this.guarantorCitizenshipForeign = page.locator(
      "#guarantorCitizenshipForeign_0"
    );

    this.guarantorNote = page.locator("#guarantorNote");

    // Actions
    this.addGuarantorBtn = page.locator("#addGuar");
    this.removeGuarantorBtn = page.locator(
      "a.btn-danger[onclick*='removeAdditionalGuarantors']"
    );

    // General Notes
    this.guarantorNotes = page.locator("#guarantorNotes");
    this.borrowerNotes = page.locator("#mortgageNotes");
  }

  // --- Reusable helpers ---
  async selectHasAdditionalGuarantors(value: "Yes" | "No") {
    // wait for any overlay/spinner to disappear
    await this.page
      .locator(".blockUI.blockOverlay")
      .waitFor({ state: "detached", timeout: 10000 });

    if (value === "Yes") {
      await this.additionalGuarantorsYes.scrollIntoViewIfNeeded();
      await this.additionalGuarantorsYes.check({ force: true });
    } else {
      await this.additionalGuarantorsNo.scrollIntoViewIfNeeded();
      await this.additionalGuarantorsNo.check({ force: true });
    }
  }

  async fillGuarantorForm(data: any) {
    await this.guarantorFName.fill(data.firstName);
    await this.guarantorMName.fill(data.middleName);
    await this.guarantorLName.fill(data.lastName);
    await this.guarantorPhone.fill(data.phone);
    await this.guarantorCell.fill(data.cell);
    await this.guarantorSSN.fill(data.ssn);
    await this.guarantorDOB.fill(data.dob);
    await this.guarantorDOB.click();
    await this.guarantorStatus.click();
    await this.guarantorStatus.selectOption(data.status);
    await this.guarantorAddress.fill(data.address);
    await this.guarantorCity.fill(data.city);
    await this.guarantorState.selectOption(data.state);
    await this.guarantorZip.fill(data.zip);
    await this.guarantorEmail.fill(data.email);
    await this.guarantorCreditScore.fill(String(data.creditScore));

    // Citizenship
    switch (data.citizenship) {
      case "US":
        await this.guarantorCitizenshipUS.check({ force: true });
        break;
      case "Perm":
        await this.guarantorCitizenshipPerm.check({ force: true });
        break;
      case "NonPerm":
        await this.guarantorCitizenshipNonPerm.check({ force: true });
        break;
      case "Foreign":
        await this.guarantorCitizenshipForeign.check({ force: true });
        break;
    }

    await this.guarantorNote.fill(data.note);
    await this.guarantorNotes.fill(data.generalNote);
    await this.borrowerNotes.fill(data.additionalNotes);
  }

  async validateGuarantorForm(expected: any) {
    await expect(this.guarantorFName).toHaveValue(expected.firstName);
    await expect(this.guarantorMName).toHaveValue(expected.middleName);
    await expect(this.guarantorLName).toHaveValue(expected.lastName);
    await expect(this.guarantorPhone).toHaveValue(expected.phone);
    await expect(this.guarantorCell).toHaveValue(expected.cell);
    await expect(this.guarantorSSN).toHaveValue(expected.ssn);
    await expect(this.guarantorDOB).toHaveValue(expected.dob);
    await expect(this.guarantorStatus).toHaveValue(expected.status);
    await expect(this.guarantorAddress).toHaveValue(expected.address);
    await expect(this.guarantorCity).toHaveValue(expected.city);
    await expect(this.guarantorState).toHaveValue(expected.state);
    await expect(this.guarantorZip).toHaveValue(expected.zip);
    await expect(this.guarantorEmail).toHaveValue(expected.email);
    await expect(this.guarantorCreditScore).toHaveValue(
      String(expected.creditScore)
    );
    await expect(this.guarantorNote).toHaveValue(expected.note);
    await expect(this.guarantorNotes).toHaveValue(expected.generalNote);
    await expect(this.borrowerNotes).toHaveValue(expected.additionalNotes);
  }

  // --- Flows ---
  async createGuarantor(data: any) {
    await this.selectHasAdditionalGuarantors("Yes");
    await this.fillGuarantorForm(data);
  }

  async updateGuarantor(updated: any) {
    await this.fillGuarantorForm(updated);
  }

  async deleteGuarantor() {
    await this.removeGuarantorBtn.click();
    await this.guarantorNotes.fill("");
    await this.borrowerNotes.fill("");
  }

  async addAnotherGuarantor(data: any) {
    await this.addGuarantorBtn.click();
    // Assuming the new guarantor fields have unique IDs or can be selected differently
    // For simplicity, reusing the same locators which may need adjustment based on actual HTML
    await this.fillGuarantorForm(data);
  }

  async fillAdditionalNotes(note: string) {
    await this.guarantorNotes.fill(note);
  }

  // support/pageObjects/additionalGuarantors.ts
  async validateAllFieldsCleared() {
    await expect(this.guarantorFName).toHaveValue("");
    await expect(this.guarantorMName).toHaveValue("");
    await expect(this.guarantorLName).toHaveValue("");
    await expect(this.guarantorPhone).toHaveValue("");
    await expect(this.guarantorCell).toHaveValue("");
    await expect(this.guarantorSSN).toHaveValue("");
    await expect(this.guarantorDOB).toHaveValue("");
    await expect(this.guarantorStatus).toHaveValue("");
    await expect(this.guarantorAddress).toHaveValue("");
    await expect(this.guarantorCity).toHaveValue("");
    await expect(this.guarantorState).toHaveValue("");
    await expect(this.guarantorZip).toHaveValue("");
    await expect(this.guarantorEmail).toHaveValue("");
    await expect(this.guarantorCreditScore).toHaveValue(""); // if default is 0
    // For radio buttons, verify none are checked
    await expect(this.guarantorCitizenshipUS).not.toBeChecked();
    await expect(this.guarantorCitizenshipPerm).not.toBeChecked();
    await expect(this.guarantorCitizenshipForeign).not.toBeChecked();
    await expect(this.guarantorNote).toHaveValue("");
    await expect(this.borrowerNotes).toHaveValue("");
    await expect(this.guarantorNotes).toHaveValue("");
  }
}
