// support/AppManager/PageObjectManager.ts
import { Page } from "@playwright/test";
import { borrowerInfoTab } from "../pageObjects/borrowerInfoTab";
import { borrowerBackground } from "../pageObjects/borrowerBackground";
import { loginPage } from "../pageObjects/LoginPage";
import { Utilities } from "../Uitility/Utilities";
import { AdditionalGuarantors } from "../pageObjects/additionalGuarantors";
import { borrowerTypeEntityInfoSection } from "../pageObjects/borrowerTypeEntityInfoSection";
import { adminInfoSection } from "../pageObjects/adminInfoSection";
import { borrowerInfoSection } from "../pageObjects/borrowerInfoSection";
import { sbaquestionsSection } from "../pageObjects/sbaQuestions";
import { coBorrowerInfoSection } from "../pageObjects/coBorrowerInfoSection";

import testData from "../../fixture/constant.json" assert { type: "json" };
import borrowerData from "../../fixture/borrowerInfoData.json" assert { type: "json" };
import borrowerUpdateData from "../../fixture/borrowerInfoUpdateData.json" assert { type: "json" };
import borrowerBackgroundData from "../../fixture/borrowerBackgroundData.json" assert { type: "json" };
import additionalGuarantorsData from "../../fixture/additionalGuarantorsData.json" assert { type: "json" };
import borrowerEntityTypeData from "../../fixture/borroweEntityTypeData.json" assert {type: "json"}
import coBorrowerInfoData from "../../fixture/coBorrowerInfoData.json" assert {type: "json"}




// import { borrowerInfoFaker } from "../../fixture/borrowerInfoFaker"; // uncomment if needed

export class AppManager {
  utilities: Utilities;
  loginInPage: loginPage;
  borrowerInfoTab: borrowerInfoTab;
  borrowerBackground: borrowerBackground;
  additionalGuarantors: AdditionalGuarantors;
  borrowerTypeEntityInfoSection: borrowerTypeEntityInfoSection;
  adminInfoSection: adminInfoSection
  borrowerInfoSection: borrowerInfoSection
  sbaquestionsSection: sbaquestionsSection
  coBorrowerInfoSection: coBorrowerInfoSection

  // fixture data
  data: typeof testData;
  borrowerInfoData: typeof borrowerData;
  borrowerInfoUpdateData: typeof borrowerUpdateData;

  borrowerBackgroundCreateData: typeof borrowerBackgroundData.create;
  borrowerBackgroundUpdateData: typeof borrowerBackgroundData.update;
  borrowerBackgroundDeleteData: typeof borrowerBackgroundData.delete;
  borrowerEntityTypeData: typeof borrowerEntityTypeData
  additionalGuarantorsData: typeof additionalGuarantorsData;
  coBorrowerInfoData:typeof coBorrowerInfoData;

  constructor(page: Page) {
    this.utilities = new Utilities();
    this.loginInPage = new loginPage(page);
    this.borrowerInfoTab = new borrowerInfoTab(page);
    this.borrowerBackground = new borrowerBackground(page, this.utilities);
    this.additionalGuarantors = new AdditionalGuarantors(page);
    this.borrowerTypeEntityInfoSection = new borrowerTypeEntityInfoSection(page)
    this.adminInfoSection = new adminInfoSection(page);
    this.borrowerInfoSection = new borrowerInfoSection(page);
    this.sbaquestionsSection = new sbaquestionsSection(page);
    this.coBorrowerInfoSection = new coBorrowerInfoSection(page);

    // fixture assignments
    this.data = testData;
    this.borrowerInfoData = borrowerData;
    this.borrowerInfoUpdateData = borrowerUpdateData;
    this.borrowerBackgroundCreateData = borrowerBackgroundData.create;
    this.borrowerBackgroundUpdateData = borrowerBackgroundData.update;
    this.borrowerBackgroundDeleteData = borrowerBackgroundData.delete;
    this.borrowerEntityTypeData = borrowerEntityTypeData;
    this.additionalGuarantorsData = additionalGuarantorsData;
    this.coBorrowerInfoData = coBorrowerInfoData;
  }

  // Returns fresh faker object each time
  getFakeBorrowerData() {
    // return borrowerInfoFaker.createLoanData(); // if you want faker
    return {}; // fallback
  }
}
