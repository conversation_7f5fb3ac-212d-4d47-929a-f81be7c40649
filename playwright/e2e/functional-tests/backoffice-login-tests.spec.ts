import { test, expect } from '@playwright/test';
import { AppManager } from '../../support/AppManager/PageObjectManager';


test('Validate login page test', async ({ page }) => {
    const app = new AppManager(page);

    await test.step('Navigate to the login page and validate login ui', async () => {
        await app.utilities.logStep("Validate login page UI");
        await page.goto('/backoffice/');
        await expect(page).toHaveURL(/login/);
        await app.loginInPage.validateLoginPageUI(app.data);
    });

    await test.step('Navigate to the login page and validate login error message', async () => {
        await app.utilities.logStep("Validate login error message");
        await page.goto('/backoffice/');
        await expect(page).toHaveURL(/login/);
        await app.loginInPage.validateLoginPageErrorMessage(app.data);
    });

    await test.step('Navigate to the login page and validate login with invalid email and password', async () => {
        await app.utilities.logStep("Validate login with invalid email and password");
        await page.goto('/backoffice/');
        await expect(page).toHaveURL(/login/);
        await app.loginInPage.validateLoginWithInvalidEmailAndPassword(app.data);
    });

    await test.step('Navigate to the login page and validate login with valid email and invalid password', async () => {
        await app.utilities.logStep("Validate login with valid email and invalid password");
        await page.goto('/backoffice/');
        await expect(page).toHaveURL(/login/);
        await app.loginInPage.validateLoginWithValidEmailAndInvalidPassword(app.data);
    });

    await test.step('Navigate to the login page and validate login with valid email and password', async () => {
        await app.utilities.logStep("Validate login with valid email and password");
        await page.goto('/backoffice/');
        await expect(page).toHaveURL(/login/);
        await app.loginInPage.validateLoginWithValidEmailAndPassword(app.data);
    });

    await test.step('Validate “Forget Password?” link is present below login button and redirects correctly', async () => {
        await app.utilities.logStep("Validate “Forget Password?” link is present below login button and redirects correctly");
        await page.goto('/backoffice/');
        await expect(page).toHaveURL(/login/);
        await app.loginInPage.validateForgetPasswordLink(app.data);
    });

    await test.step('Validate User should redirect to update billing page', async () => {
        await app.utilities.logStep("Validate User should redirect to update billing page");
        await page.goto('/backoffice/');
        await expect(page).toHaveURL(/login/);
        await app.loginInPage.validateUpdateBillingLink(app.data);
    });

    await test.step('Validate User should redirect to landingwise home page after click on logo', async () => {
        await app.utilities.logStep("Validate User should redirect to landingwise home page after click on logo");
        await page.goto('/backoffice/');
        await expect(page).toHaveURL(/login/);
        await app.loginInPage.validateLandingWiseHomePageNavigation();
    });
});
