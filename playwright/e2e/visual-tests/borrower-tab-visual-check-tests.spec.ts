import test from "playwright/test"
import { AppManager } from "../../support/AppManager/PageObjectManager";

test.describe('first', () => {
    test.skip('Validate Borrower Info Tab UI', async ({ page }) => {
        const app = new AppManager(page);
        await test.step('Validate Borrower Info Tab UI', async () => {
            app.utilities.logStep("Validate the borrower info tab for admin info section UI changes");
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("BorrowerInfoTab_AdminInfoSection");
            await app.adminInfoSection.clickOncloseAllOpenTabsButton();
            await app.adminInfoSection.openAdminInfoSection();
            await app.adminInfoSection.takeScreenShotOfAdminInfoSectionAndValidate("BorrowerInfoTab_AdminInfoSection");
            // await app.borrowerInfoTab.takeScreenShotOfLoanFileAndValidate("BorrowerInfoTab_LoanFile");
            // await app.borrowerInfoTab.takeFullElementScreenshot("BorrowerInfoTab_LoanFile");
        });
    });
});