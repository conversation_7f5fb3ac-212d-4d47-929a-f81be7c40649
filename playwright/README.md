# Playwright Test Automation Framework

A robust end-to-end testing framework built with <PERSON><PERSON> for automated web application testing.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Project Structure](#project-structure)
- [Configuration](#configuration)
- [Running Tests](#running-tests)
- [Test Reports](#test-reports)
- [Writing Tests](#writing-tests)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version 16.x or higher
- **npm**: Version 8.x or higher (comes with Node.js)
- **Git**: For version control

### Verify Installation

```bash
node --version
npm --version
```

## 📦 Installation

1. **Clone the repository**

```bash
git clone <repository-url>
cd playwright
```

2. **Install dependencies**

```bash
npm install
```

3. **Install Playwright browsers**

```bash
npx playwright install-deps
npx playwright install
```

This will download Chromium, Firefox, and WebKit browsers that <PERSON><PERSON> uses for testing.

## 📁 Project Structure

```
playwright/
├── allure-results/          # Allure test results (generated)
├── config/                  # Configuration files
├── e2e/                     # End-to-end test files
│   ├── api-tests/          # API test cases
│   ├── functional-tests/   # Functional test cases
│   └── visual-tests/       # Visual regression tests
├── fixture/                 # Test fixtures and data
│   ├── auth/               # Authentication related fixtures
│   └── screenshots/        # Screenshot storage for visual tests
├── node_modules/           # Node dependencies (generated)
├── report/                 # Test reports (generated)
├── support/                # Support files and utilities
│   ├── AppManager/         # Application manager
│   ├── pageObjects/        # Page Object Model classes
│   └── Utility/            # Utility functions and helpers
├── .gitignore             # Git ignore configuration
├── constant.json          # Constants and configuration values
├── package-lock.json      # Locked versions of dependencies
├── package.json           # Project dependencies and scripts
└── playwright.config.ts   # Playwright configuration
```

## ⚙️ Configuration

### Environment Variables

The framework supports multiple test environments using the `TEST_ENV` variable:

- `pre-prod`: Pre-production environment (default)

### Playwright Configuration

Key configuration options in `playwright.config.ts`:


## 🚀 Running Tests

### Run All Tests

```bash
# Run tests in headed mode (with browser UI) on pre-prod environment
npm run pl:pre-prod

# Run tests in headless mode
npx playwright test

# Run specific test type
npx playwright test e2e/functional-tests
npx playwright test e2e/api-tests
npx playwright test e2e/visual-tests

# Run specific test file
npx playwright test e2e/functional-tests/test_file_name.spec.ts

# Run tests in debug mode
npx playwright test --debug

# Run tests with specific browser
npx playwright test --project=chromium
```

### Test Execution Options

```bash
# Run tests in parallel
npx playwright test --workers=4

# Run tests with retries
npx playwright test --retries=2

# Run tests matching specific pattern
npx playwright test -g "login"
```

## 📊 Test Reports

### Generate Allure Report

1. **Run tests to generate results**
```bash
npm run pl:pre-prod
```

2. **Generate HTML report**
```bash
npm run generate:report
```

3. **Open the report**
```bash
npx allure open ./report/allure-report
```

### Clean Previous Reports

```bash
npm run clean:report
```

### Built-in Playwright Report

```bash
# Show last test run report
npx playwright show-report
```

### 6. **Organize Tests by Feature**
```
e2e/
├── api-tests/
│   ├── auth/
│   │   ├── login.spec.ts
│   │   └── logout.spec.ts
│   └── user/
│       └── profile.spec.ts
├── functional-tests/
│   ├── borrower/
│   │   └── borrowerInfo.spec.ts
│   └── loan/
│       └── loanApplication.spec.ts
└── visual-tests/
    ├── homepage.spec.ts
    └── dashboard.spec.ts
```

## 🔍 Debugging

### Debug Mode
```bash
# Run with debug mode
npx playwright test --debug

# Set breakpoint in code
await page.pause();
```

### View Trace
```bash
# Run with trace
npx playwright test --trace on

# View trace
npx playwright show-trace trace.zip
```

### Console Logs
```javascript
page.on('console', msg => console.log(msg.text()));
```

## 🐛 Troubleshooting

### Common Issues

1. **Browser download fails**
```bash
# Manually install browsers
npx playwright install --with-deps
```

2. **Tests timeout**
- Increase timeout in config or specific test
```javascript
test.setTimeout(60000); // 60 seconds
```

3. **Element not found**
- Check if element is in iframe
- Wait for element to be visible
- Verify selector is correct

4. **Screenshot comparison fails**
- Update screenshots: `npx playwright test --update-snapshots`
- Check for OS-specific rendering differences

### Environment-Specific Issues

- **Linux/CI**: May need additional dependencies
```bash
npx playwright install-deps
```

- **Windows**: Path issues with screenshots
```javascript
const screenshotPath = path.join(__dirname, 'screenshots', 'test.png');
```

## 📚 Additional Resources

- [Playwright Documentation](https://playwright.dev)
- [Allure Report Documentation](https://docs.qameta.io/allure/)
- [Page Object Model Pattern](https://martinfowler.com/bliki/PageObject.html)

## 🤝 Contributing

1. Create a feature branch
2. Write tests for new functionality
3. Ensure all tests pass
4. Submit a pull request

## 📄 License

This project is licensed under the ISC License.