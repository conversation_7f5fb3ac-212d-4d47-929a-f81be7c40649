{"fileNameFirstName": "<PERSON>", "fileNameLastName": "<PERSON><PERSON><PERSON>", "middleName": "<PERSON>", "primaryEmail": "<EMAIL>", "secondaryEmail": "<EMAIL>", "homePhone": "(987) 654 - 3210 Ext 1234", "cellPhone": "(912) 345 - 6789", "fax": "(011) 223 - 3445", "serviceProvider": "att", "preferredCommunication": "Phone", "alternatePhone": "(987) 654 - 3210 Ext 1234", "workNumber": "(022) 334 - 4556 Ext 6123", "presentAddress": "123 Main Street", "presentUnit": "Apt 4B", "presentCity": "New York", "presentState": "NY", "presentZip": "10001", "presentCounty": "Erie", "presentCountry": "US", "presentPropLengthYears": "5", "presentPropLengthMonths": "6", "rentOrOwn": "Own", "currentRPM": "2,500.00", "formerAddress": "123 Old Street", "formerUnit": "Apt 12", "formerCity": "Beijing", "formerState": "HI", "formerZip": "40001", "formerCountry": "CN", "formerRentOrOwn": "Rent", "lengthOfTimeYears": "2", "lengthOfTimeMonths": "12", "rentPerMonth": "25,000.00", "mailingAddress": "123 Main Street", "mailingUnit": "Apt 4B", "mailingCity": "New York", "mailingState": "NY", "mailingZip": "10001", "mailingCountry": "US", "alternateFirstName": "<PERSON>", "alternateMiddleName": "M", "alternateLastName": "<PERSON><PERSON>", "dob": "04/10/1997", "placeOfBirth": "New York", "ssn": "123 - 45 - 6789", "driverLicenseState": "NY", "driverLicenseNumber": "*********", "driverLicenseIssuanceDate": "04/10/2019", "driverLicenseExpirationDate": "04/10/2035", "maritalStatusUnmarried": true, "maritalStatusMarried": true, "marriageDate": "05/15/2020", "divorceDate": "06/18/2022", "spouseName": "<PERSON>", "maidenName": "<PERSON>", "maritalStatusSeparated": true, "citizenshipUS": true, "citizenshipPermResident": true, "citizenshipNonPermResident": true, "citizenshipForeignNational": true, "servicingMemberNo": true, "servicingMemberYes": true, "servicingMemberInfoOptions": ["Currently serving on active duty", "Currently retired,discharged, or separated from service", "Only period of service was as a non-activated member of the Reserve or National Guard", "Surviving Spouse"], "serviceExpirationDate": "12/31/2030", "agesOfDependent": "12", "numberOfDependents": "2", "midFicoScore": "700", "equifaxScore": "680", "transunionScore": "690", "experianScore": "710", "creditScoreRange": "800+", "authorizationStatuses": ["Complete", "Need All", "Have 1 & Need 1", "Have 1 & Need 2", "Have 1 & Need 3", "Have 2 & Need 1", "Have 2 & Need 2"]}