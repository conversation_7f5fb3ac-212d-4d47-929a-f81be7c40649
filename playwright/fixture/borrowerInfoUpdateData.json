{"fileNameFirstName": "<PERSON>", "fileNameLastName": "Jordan", "middleName": "<PERSON>", "primaryEmail": "mi<PERSON><PERSON>@example.com", "secondaryEmail": "<EMAIL>", "homePhone": "(555) 111 - 2222 Ext 5678", "cellPhone": "(555) 333 - 4444", "fax": "(555) 999 - 8888", "serviceProvider": "cricket", "preferredCommunication": "Email", "alternatePhone": "(555) 777 - 6666 Ext 4321", "workNumber": "(555) 222 - 1111 Ext 9876", "presentAddress": "456 Elm Avenue", "presentUnit": "Suite 10C", "presentCity": "Los Angeles", "presentState": "CA", "presentZip": "90001", "presentCounty": "Erie", "presentCountry": "FR", "presentPropLengthYears": "10", "presentPropLengthMonths": "3", "rentOrOwn": "Rent", "currentRPM": "3,200.00", "formerAddress": "789 Pine Street", "formerUnit": "Unit 22", "formerCity": "Chicago", "formerState": "IL", "formerZip": "60601", "formerCountry": "US", "formerRentOrOwn": "Own", "lengthOfTimeYears": "4", "lengthOfTimeMonths": "5", "rentPerMonth": "3,800.00", "mailingAddress": "456 Elm Avenue", "mailingUnit": "Suite 10", "mailingCity": "Los Angeles", "mailingState": "CA", "mailingZip": "90001", "mailingCountry": "US", "alternateFirstName": "<PERSON>", "alternateMiddleName": "K", "alternateLastName": "<PERSON>", "dob": "01/15/1985", "placeOfBirth": "Chicago", "ssn": "987 - 65 - 4321", "driverLicenseState": "CA", "driverLicenseNumber": "*********", "driverLicenseIssuanceDate": "01/20/2020", "driverLicenseExpirationDate": "01/20/2030", "maritalStatusUnmarried": false, "maritalStatusMarried": true, "marriageDate": "02/14/2015", "divorceDate": "03/10/2021", "spouseName": "<PERSON>", "maidenName": "<PERSON>", "maritalStatusSeparated": false, "citizenshipUS": false, "citizenshipPermResident": true, "citizenshipNonPermResident": false, "citizenshipForeignNational": false, "servicingMemberNo": true, "servicingMemberYes": false, "servicingMemberInfoOptions": ["Currently retired,discharged, or separated from service"], "serviceExpirationDate": "11/30/2035", "agesOfDependent": "8", "numberOfDependents": "3", "midFicoScore": "720", "equifaxScore": "700", "transunionScore": "710", "experianScore": "730", "creditScoreRange": "780-799", "authorizationStatuses": ["Have 1 & Need 3"]}