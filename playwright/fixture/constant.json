{"login": {"validEmail": "<EMAIL>", "validPassword": "Arif2024$", "invalidEmail": "<EMAIL>", "invalidPassword": "test123"}, "messages": {"header": "Back Office User", "placeholders": {"email": "Email", "password": "Password"}, "errors": {"emptyEmail": "Please Enter Email", "emptyPassword": "Please Enter Password", "invalidCredentials": "Account Not Found / Invalid Password"}, "links": {"forgotPassword": "Forget Password ?", "updateBilling": "Update Billing", "forgotPasswordHeader": "Forgotten Password ?", "subscriptionHeader": "Manage Your Subscriptions"}, "buttons": {"login": "<PERSON><PERSON>"}}, "borrowerInfo": {"branch": {"noBranch": "", "branchName": "JDAQA"}, "brokerPartner": {"nobrokerPartner": "", "brokerName": "<PERSON> - <EMAIL>"}, "loanOfficer": {"noLoanOfficer": "", "loanOfficerName": "Autumn Bruno - <EMAIL>", "loanOfficerName2": "MD JDAQA - <EMAIL>"}, "fileType": {"noFileType": "", "fileTypeName": "Commercial/Residential Real Estate", "fileTypeName2": "Business Funding"}, "loanProgram": {"noLoanProgram": "", "loanProgramName": "SBA-7a", "loanProgramName2": "All Fields On"}, "sbaLoanProduct": {"noSbaLoanProduct": "", "sbaLoanProductName": "SBA 7a"}, "additionalDesiredLoanPrograms": {"noAdditionalDesiredLoanPrograms": "", "additionalDesiredLoanProgramsName": "SBA-7a", "additionalDesiredLoanProgramsName2": "All Fields On"}, "internalLoanProgram": {"noInternalLoanProgram": "", "internalLoanProgramName": "", "internalLoanProgramName2": "RCN Capital"}, "whereAreYouInTheProcess": {"noWhereAreYouInTheProcess": "", "whereAreYouInTheProcessName": "Actively Looking For Property", "whereAreYouInTheProcessName2": "Looking for Basic Info"}, "primaryClientFileStatus": {"noPrimaryClientFileStatus": "", "primaryClientFileStatusName": "Lead"}, "fileSubStatus": {"noFileSubStatus": "", "fileSubStatusName": "120 day past due", "fileSubStatusName2": "15 day past due"}, "leadResource": {"noLeadResource": "", "leadResourceName": ""}, "projectName": {"noProjectName": "", "projectNameValue": "Test Project For Playewright Automation"}, "calenderFeildReceived": {"index": 0, "date": "08/19/2025", "nodate": ""}, "calenderFeildBorrowerCallback": {"index": 1, "date": "09/19/2025", "nodate": ""}, "welcomeCallDate": {"index": 2, "date": "08/30/2025", "nodate": ""}, "actualClosingDate": {"index": 3, "date": "08/26/2025", "nodate": ""}, "targetClosingDate": {"index": 4, "date": "09/20/2025", "nodate": ""}, "hearingDate": {"index": 5, "date": "10/19/2025", "nodate": ""}, "disclosureSentDate": {"index": 6, "date": "11/19/2025", "nodate": ""}, "loanDocumentDate": {"index": 7, "date": "12/19/2025", "nodate": ""}, "thirdPartyId": {"id": "234321", "noid": ""}}, "SBAQuetions": {"monthlyPayroll": {"noamount": "", "amount": 20}, "purposeOfLoan": {"purposeOfLoan1": "Payroll", "purposeOfLoan2": "Rent/Mortgage Interest", "purposeOfLoan3": "Utilities", "purposeOfLoan4": "Other"}, "detailExplaination": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "criminalExplanation": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "arrestedExplanation": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "criminaloffenceExplanation": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "certifiedDeveExplanationExplanation": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "SBAFranchiseDirectory": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "SmallBusinessApplicant": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "SmallBusinessApplicantBankruptcy": {"optionToSelect": "1-3 months", "explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "SmallBusinessApplicantLegal": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "DebarredSuspendedFederal": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "ChildSupportEnforcementServices": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "SbaLoanOption": {"explanation1": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./", "explanation2": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "SmallBusinessApplicantExport": {"amount": 166.66}, "SmallBusinessApplicantPacker": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "SmallBusinessApplicantRevenue": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "NoSBAempHouseHoldMember": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "NoFormerSBAempSeparated": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "NoMemberSoleProprietor": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "NoGovEmpGS13": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "NoMemberSmallBuinessAdvisory": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "HaveControlledBankruptcyProtection": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "ApplicantsPayrollCalculation": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "EstimatedMonthlyPayroll": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "LossToTheGovernment": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "BusinessControlLegalAction": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "SbaEconomicInjury": {"amount": 56000, "explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "HaveOwnershipAffiliate": {"businessinfo": {"companyname": "<PERSON><PERSON>", "taxid": 23, "legalowners": 54, "idofemp": 334, "yrinbusiness": 10, "appliedforloan": "Yes", "description": "Hi, Welcome to testing"}, "explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*()_+`-={}|[]:;'<>?,./"}, "W2Employeescount": {"count": 10}, "W2issuesin2020": {"count": 5}, "Companystarted": {"novalue": "", "value": "Before Jan 1, 2019"}, "impactedExplanation": {"value": "How your business was impacted during the partial"}, "affectedQuarters1": {"value": "Q3 2020: Jul – Sep"}, "grossReceipts1": {"Q1_2019": "123400", "Q2_2019": "23400", "Q3_2019": "23400", "Q4_2019": "45600", "Q1_2020": "34560", "Q2_2020": "65490", "Q3_2020": "54680", "Q4_2020": "123400", "Q1_2021": "45600", "Q2_2021": "45600", "Q3_2021": "65490", "Q4_2021": "65490"}, "impactExplanation": {"explanation": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRST"}, "affectedQuarters2": {"value": "Q4 2020: Oct – Dec"}, "grossReceipts2": {"Q1_2019": "1234", "Q2_2019": "2340", "Q3_2019": "2340", "Q4_2019": "456", "Q1_2020": "345", "Q2_2020": "654", "Q3_2020": "546", "Q4_2020": "1234", "Q1_2021": "456", "Q2_2021": "456", "Q3_2021": "654", "Q4_2021": "654"}, "QuarterExperiencedLoss2020": {"value": "Q2 2020: Apr – Jun"}, "QuarterExperiencedLoss2021": {"value": " 2021: Oct – Dec "}, "pppLoanamount": {"draw1": 326, "draw2": 111}, "pppLoanforgive": {"draw1": "No", "draw2": "Yes"}, "ForgivenAmounts": {"draw1": 32600, "draw2": 11100}, "PayrollService": {"value": "hi,please select payroll"}, "EmpRetentionCre": {"details": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVW"}, "EmployeeFamilyOwner": {"value": "<PERSON>"}, "Startedotherbusiness": {"value": "jkahfhfhajhdfjahjfhajhjaddDSds"}, "OtherExistingBusiness": {"value": "jkahfhfhajhdfjahjfhajhjaddDSds"}}}