import { faker } from "@faker-js/faker";

export class borrowerInfoFaker {
  static createLoanData() {
    return {
      fileNameFirstName: faker.person.firstName(),
      fileNameLastName: faker.person.lastName(),

      middleName: faker.person.middleName(),
      primaryEmail: faker.internet.email(),
      secondaryEmail: faker.internet.email(),
      homePhone: faker.helpers.replaceSymbols("(###) ### - #### Ext ####"),
      cellPhone: faker.helpers.replaceSymbols("(###) ### - ####"),
      fax: faker.helpers.replaceSymbols("(###) ### - ####"),

      serviceProvider: faker.company.name(),
      preferredCommunication: faker.helpers.arrayElement([
        "Phone",
        "Email",
        "Text",
      ]),
      alternatePhone: faker.helpers.replaceSymbols("(###) ### - #### Ext ####"),
      workNumber: faker.helpers.replaceSymbols("(###) ### - #### Ext ####"),

      presentAddress: faker.location.streetAddress(),
      presentUnit: faker.location.secondaryAddress(),
      presentCity: faker.location.city(),
      presentState: faker.location.state(),
      presentZip: faker.location.zipCode("#####"),
      presentCounty: faker.location.county(),
      presentCountry: faker.location.country(),

      presentPropLengthYears: faker.number.int({ min: 0, max: 20 }).toString(),
      presentPropLengthMonths: faker.number.int({ min: 0, max: 11 }).toString(),
      rentOrOwn: faker.helpers.arrayElement(["Rent", "Own"]),
      currentRPM: faker.number.int({ min: 1000, max: 5000 }).toString(),

      formerAddress: faker.location.streetAddress(),
      formerUnit: faker.location.secondaryAddress(),
      formerCity: faker.location.city(),
      formerState: faker.location.state(),
      formerZip: faker.location.zipCode("#####"),
      formerCountry: faker.location.country(),
      formerRentOrOwn: faker.helpers.arrayElement(["Rent", "Own"]),
      lengthOfTimeYears: faker.number.int({ min: 0, max: 10 }).toString(),
      lengthOfTimeMonths: faker.number.int({ min: 0, max: 11 }).toString(),
      rentPerMonth: faker.number.int({ min: 500, max: 50000 }).toString(),

      mailingAddress: faker.location.streetAddress(),
      mailingUnit: faker.location.secondaryAddress(),
      mailingCity: faker.location.city(),
      mailingState: faker.location.state({ abbreviated: true }),
      mailingZip: faker.location.zipCode("#####"),
      mailingCountry: faker.location.countryCode("alpha-2"),

      alternateFirstName: faker.person.firstName(),
      alternateMiddleName: faker.person.middleName(),
      alternateLastName: faker.person.lastName(),

      dob: faker.date
        .birthdate({ min: 18, max: 65, mode: "age" })
        .toLocaleDateString("en-US"),
      placeOfBirth: faker.location.city(),

      ssn: faker.string
        .numeric(9)
        .replace(/(\d{3})(\d{2})(\d{4})/, "$1 - $2 - $3"),
      driverLicenseState: faker.location.state({ abbreviated: true }),
      driverLicenseNumber: `DL${faker.string.alphanumeric(7).toUpperCase()}`,
      driverLicenseIssuanceDate: faker.date
        .past({ years: 10 })
        .toLocaleDateString("en-US"),
      driverLicenseExpirationDate: faker.date
        .future({ years: 10 })
        .toLocaleDateString("en-US"),

      maritalStatusUnmarried: faker.datatype.boolean(),
      maritalStatusMarried: faker.datatype.boolean(),
      marriageDate: faker.date.past({ years: 5 }).toLocaleDateString("en-US"),
      divorceDate: faker.date.past({ years: 2 }).toLocaleDateString("en-US"),
      spouseName: `${faker.person.firstName()} ${faker.person.lastName()}`,
      maidenName: faker.person.lastName(),
      maritalStatusSeparated: faker.datatype.boolean(),

      citizenshipUS: faker.datatype.boolean(),
      citizenshipPermResident: faker.datatype.boolean(),
      citizenshipNonPermResident: faker.datatype.boolean(),
      citizenshipForeignNational: faker.datatype.boolean(),

      servicingMemberNo: faker.datatype.boolean(),
      servicingMemberYes: faker.datatype.boolean(),
      servicingMemberInfoOptions: [
        "Currently serving on active duty",
        "Currently retired,discharged, or separated from service",
        "Only period of service was as a non-activated member of the Reserve or National Guard",
        "Surviving Spouse",
      ],
      serviceExpirationDate: faker.date
        .future({ years: 10 })
        .toLocaleDateString("en-US"),

      agesOfDependent: faker.number.int({ min: 1, max: 21 }).toString(),
      numberOfDependents: faker.number.int({ min: 0, max: 5 }).toString(),
      midFicoScore: faker.number.int({ min: 650, max: 850 }).toString(),
      equifaxScore: faker.number.int({ min: 650, max: 850 }).toString(),
      transunionScore: faker.number.int({ min: 650, max: 850 }).toString(),
      experianScore: faker.number.int({ min: 650, max: 850 }).toString(),
      creditScoreRange: faker.helpers.arrayElement([
        "650 - 699",
        "700 - 749",
        "750 +",
      ]),

      authorizationStatuses: [
        "Complete",
        "Need All",
        "Have 1 & Need 1",
        "Have 1 & Need 2",
        "Have 1 & Need 3",
        "Have 2 & Need 1",
        "Have 2 & Need 2",
      ],
    };
  }
}
