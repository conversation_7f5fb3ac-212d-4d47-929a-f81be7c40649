{"create": {"firstName": "<PERSON>", "middleName": "A", "lastName": "<PERSON><PERSON>", "phone": "(123) 456 - 7890 Ext 1234", "cell": "(987) 654 - 3210", "ssn": "123 - 45 - 6789", "dob": "01/01/1990", "status": "Single", "address": "123 Main St", "city": "New York", "state": "NY", "zip": "10001", "email": "<EMAIL>", "creditScore": 720, "citizenship": "US", "note": "Test guarantor note", "generalNote": "General note create", "additionalNotes": "Test notes create"}, "update": {"firstName": "<PERSON>", "middleName": "B", "lastName": "<PERSON>", "phone": "(111) 222 - 3333 Ext 1234", "cell": "(444) 555 - 6666", "ssn": "987 - 65 - 4321", "dob": "02/02/1992", "status": "Married", "address": "456 Broadway", "city": "Boston", "state": "MA", "zip": "02118", "email": "<EMAIL>", "creditScore": 680, "citizenship": "Perm", "note": "Updated guarantor note", "generalNote": "General note update", "additionalNotes": "Test notes update"}}