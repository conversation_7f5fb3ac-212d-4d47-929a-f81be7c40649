<?php
global $PCID, $emailOpt, $LMRId, $allowToEdit,
       $fOpt, $REBroker, $REBrokerYesBtn, $BrokerInfoDivDisp,
       $preferredBrokerInfoArray, $agentNumber, $brokerFName, $brokerLName,
       $brokerEmail, $brokerCompany, $brokerPhNo1, $brokerPhNo2, $brokerPhNo3,
       $brokerExt, $brokerPhone, $addBranchHearAbout, $publicUser,
       $showLimitedMandatoryField, $branchHAInfoArray, $leadSource, $servicesRequested,
       $LMRClientTypeInfo, $ClientType, $borrowerFName, $borrowerLName,
       $entityName, $borrowerEmail, $phNo1, $phNo2, $phNo3, $borrowerDOB,
       $ssn1, $ssn2, $ssn3, $ssnNumber,
       $borCreditScoreRange, $cellNo1, $cellNo2, $cellNo3, $processingCompanyId,
       $serviceProvider, $haveBorRehabConstructionExperience, $showRCDispOpt,
       $borRehabPropCompleted, $landValueCls, $isOwnLand,
       $presentAddress, $presentCity, $stateArray, $presentState,
       $presentZip, $tabIndexNo, $borResidedPresentAddr, $propDetailsProcess,
       $presentOccupancy,
       $assetCheckingAccounts, $assetSavingMoneyMarket, $assetStocks,
       $assetIRAAccounts, $assetESPOAccounts, $totalAssets,
       $executiveId, $refinanceSectionDispOpt, $originalPurchaseDate, $refinanceCurrentLender, $originalPurchasePrice,
       $costOfImprovementsMade, $refinanceMonthlyPayment, $refinanceCurrentRate,
       $subjectPropertySectionDispOpt, $actualRentsInPlace, $lessActualExpenses,
       $netOperatingIncome, $grossAnnualRentLargestTenant, $HMLOTAC;

use models\constants\gl\glAgentLabelChanges;
use models\constants\gl\glAllowHMLOPCToEditBrokerINSV;
use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOPresentOccupancy;
use models\constants\gl\glMandatoryFieldForPC;
use models\constants\gl\glPCID;
use models\constants\gl\glPropDetailsProcess;
use models\constants\gl\glPropertyCondition;
use models\constants\GpropertyTypeNumbArray;
use models\constants\SMSServiceProviderArray;
use models\Controllers\LMRequest\Property;
use models\cypher;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Strings;

$SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::$glHMLOCreditScoreRange;
$glHMLOPresentOccupancy = ($PCID == glPCID::PCID_PROD_CV3) ? glHMLOPresentOccupancy::$glHMLOPresentOccupancyCV3 :
    $glHMLOPresentOccupancy = glHMLOPresentOccupancy::$glHMLOPresentOccupancy;
$glPropDetailsProcess = glPropDetailsProcess::$glPropDetailsProcess;
$glPropertyCondition = glPropertyCondition::$glPropertyCondition;
$glMandatoryFieldForPC = glMandatoryFieldForPC::$glMandatoryFieldForPC;
$glAllowHMLOPCToEditBrokerINSV = glAllowHMLOPCToEditBrokerINSV::$glAllowHMLOPCToEditBrokerINSV;
$glAgentLabelChanges = glAgentLabelChanges::$glAgentLabelChanges;
$glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange($PCID);
?>
<script type="text/javascript">
    $(function () {
        $('#purchaseCloseDate').datepicker({autoclose:false,changeMonth: true, changeYear: true, dateFormat: 'mm/dd/yy',startDate: '01/01/1900',});
        $('#originalPurchaseDate').datepicker({
            autoclose:false,
            changeMonth: true,
            changeYear: true,
            startDate: '01/01/1900',
            dateFormat: 'mm/dd/yy'
        });
        $('#resaleClosingDate').datepicker({autoclose:false,changeMonth: true, changeYear: true, dateFormat: 'mm/dd/yy',startDate: '01/01/1900',});

    });
</script>
<?php
$tabIndex = 1;
$tabIndex++;

/**
 *
 * Customization for PC = Lendterra -->  Allow to edit the broker info in the webform on Mar 23, 2018.
 *
 * Added the Testing Purpose PC Dave = 820, Awata = 2, Lendterra = 3126 PC's
 * Ticket ID : 155638555
 **/
$allowUserToEditBroker = 0;
if (in_array($PCID, $glAllowHMLOPCToEditBrokerINSV) && $emailOpt == 'Email' && $LMRId > 0 && $allowToEdit) {
    $allowUserToEditBroker = 1;
}

/* 
 * “Agent/Broker Info” as a label “Loan Officer/Mortgage Broker Info” - Dayton Capital Partners, LLC
 * Ticket ID : 156333030
 * Mar 28, 2018
 * Make it as global on Mar 30, 2018
 **/

$agentLabel = 'Agent/Broker';
if (in_array($PCID, $glAgentLabelChanges)) {
    $agentLabel = 'Loan Officer/Mortgage Broker';
}
?>
<input type="hidden" name="fileModule[]" id="fileModule" value="HMLO"/>
<?php
if ($fOpt != 'agent') { // Agent Section Only shows Branch Web Form Only.
    $agentSectionDiv = 'display: block;';
    ?>
    <div class="clear pad10"></div>
    <div id="HMLOSectionDIV">
        <div class="block-content" width="100%"> <!-- Broker Info Section Start -->
            <div class="head">Loan Officer/Broker Info</div>
            <table style="width:100%" border="0">
                <tr>
                    <td width="30%">Are you a Loan Officer/Broker or working with one?</td>
                    <td width="70%">
                        <?php
                        if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                            <input type="radio" name="REBroker" value="Yes"
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $REBroker) . ' ' . $REBrokerYesBtn; ?>
                                   onclick="showAndHideBrokerInfo(this.value, 'BrokerInfoDiv');">Yes
                            <input type="radio" name="REBroker" value="No"
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $REBroker); ?>
                                   onclick="showAndHideBrokerInfo(this.value, 'BrokerInfoDiv');">No
                            <?php
                        } else {
                            echo '<h5>' . $REBroker . '</h5>';
                        }
                        ?>
                    </td>
                </tr>
            </table>

            <div id="BrokerInfoDiv" style="<?php echo $BrokerInfoDivDisp; ?>" class="brokerSection">
                <!-- Broker Info Section Start -->
                <table style="width:100%">
                    <?php
                    if (count($preferredBrokerInfoArray) > 0) { // It show allowed agents Start.
                        ?>
                        <tr>
                            <td>
                                <?php
                                if ($LMRId == 0 || $allowUserToEditBroker = 1) {
                                    echo 'Please select your Loan Officer/Broker';
                                } else {
                                    echo 'Loan Officer/Broker';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                    ?>
                                    <select name="LMRBroker" id="LMRBroker" style="width:150px;"
                                            tabindex="<?php echo $tabIndex++; ?>"
                                            onchange="updateBrokerNo(this.value, 'loanModForm');populateBrokerInfo('loanModForm', this.value, 'DD');">
                                        <option value=""> - Select / New -</option>
                                        <?php
                                        for ($pd = 0; $pd < count($preferredBrokerInfoArray); $pd++) {
                                            $tempPrefArray = [];
                                            $selLMRBrokerNo = 0;
                                            $selBrokerFName = '';
                                            $selBrokerLName = '';
                                            $selLMRBrokerName = '';
                                            $sOpt = '';
                                            $tempPrefArray = $preferredBrokerInfoArray[$pd];
                                            $selLMRBrokerNo = cypher::myEncryption($tempPrefArray['brokerNumber']);
                                            $selBrokerFName = $tempPrefArray['brokerFName'];
                                            $selBrokerLName = $tempPrefArray['brokerLName'];
                                            $selLMRBrokerName = $selBrokerFName . ' ' . $selBrokerLName;
                                            if ($selLMRBrokerNo == cypher::myEncryption($agentNumber)) $sOpt = 'selected';
                                            ?>
                                            <option value="<?php echo $selLMRBrokerNo; ?>" <?php echo $sOpt ?> ><?php echo $selLMRBrokerName ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                    <?php
                                } else {
                                    echo '<h5>' . $brokerFName . ' ' . $brokerLName . '</h5>';
                                }
                                ?>
                            </td>
                        </tr>
                        <?php
                    } // It show allowed agents End.
                    ?>
                    <tr class="showOnHMLO even">
                        <td width="28%">Email Address</td>
                        <td width="72%">
                            <?php
                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                ?>
                                <input type="text" class="mandatory agentInfoCls" name="REBrokerEmail"
                                       id="REBrokerEmail" tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo $brokerEmail; ?>" maxlength="75" autocomplete="off" size="40"
                                       onblur="checkREBrokerEmailExist('loanModForm');">
                                <?php
                            } else {
                                echo '<h5>' . $brokerEmail . '</h5>';
                            }
                            ?>
                        </td>
                    </tr>

                    <tr class="showOnHMLO">
                        <td>First Name</td>
                        <td>
                            <?php
                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                ?>
                                <input type="text" class="mandatory agentInfoCls" name="REBrokerFirstName"
                                       id="REBrokerFirstName" tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo $brokerFName; ?>" maxlength="30" autocomplete="off">
                                <?php
                            } else {
                                echo '<h5>' . $brokerFName . '</h5>';
                            }
                            ?>
                        </td>
                    </tr>

                    <tr class="showOnHMLO even">
                        <td>Last Name</td>
                        <td>
                            <?php
                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                ?>
                                <input type="text" class="mandatory agentInfoCls" name="REBrokerLastName"
                                       id="REBrokerLastName" tabindex="<?php echo $tabIndex++; ?>"
                                       value="<?php echo $brokerLName; ?>" maxlength="30" autocomplete="off">
                                <?php
                            } else {
                                echo '<h5>' . $brokerLName . '</h5>';
                            }
                            ?>
                        </td>
                    </tr>

                    <tr class="showOnHMLO">
                        <td>Company Name</td>
                        <td>
                            <?php
                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                ?>
                                <input type="text" name="REBrokerCompany" id="REBrokerCompany"
                                       tabindex="<?php echo $tabIndex++; ?>" class="mandatory agentInfoCls"
                                       value="<?php echo $brokerCompany; ?>" maxlength="30" autocomplete="off">
                                <?php
                            } else {
                                echo '<h5>' . $brokerCompany . '</h5>';
                            }
                            ?>
                        </td>
                    </tr>

                    <tr class="showOnHMLO even">
                        <td>Phone Number</td>
                        <td>
                            <?php
                            if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                ?>
                                <input type="hidden" name="brokerPhone" id="brokerPhone" value=""/>
                                <input type="text" name="bPhNo1" id="bPhNo1" class="mandatory agentInfoCls"
                                       tabindex="<?php echo $tabIndex++; ?>" maxlength="3" size="3"
                                       value="<?php echo $brokerPhNo1; ?>" autocomplete="off"
                                       onkeyup="return autoTab('loanModForm', 'bPhNo1', 'bPhNo2');">
                                &nbsp;- &nbsp;<input type="text" name="bPhNo2" id="bPhNo2"
                                                     class="mandatory agentInfoCls"
                                                     tabindex="<?php echo $tabIndex++; ?>" maxlength="3" size="3"
                                                     value="<?php echo $brokerPhNo2; ?>" autocomplete="off"
                                                     onkeyup="return autoTab('loanModForm', 'bPhNo2', 'bPhNo3');">
                                &nbsp;- &nbsp;<input type="text" name="bPhNo3" id="bPhNo3"
                                                     class="mandatory agentInfoCls"
                                                     tabindex="<?php echo $tabIndex++; ?>" maxlength="4" size="4"
                                                     value="<?php echo $brokerPhNo3; ?>" autocomplete="off"
                                                     onkeyup="return autoTabExt('loanModForm', 'bPhNo3', 'bExt');">
                                Ext #<input type="text" name="bExt" id="bExt" tabindex="<?php echo $tabIndex++; ?>"
                                            maxlength="10" size="11" value="<?php echo $brokerExt; ?>"
                                            autocomplete="off">
                                <?php
                            } else {
                                echo '<h5>' . Strings::formatPhoneNumber($brokerPhone) . '</h5>';
                            }
                            ?>
                        </td>
                    </tr>
                </table>
            </div> <!-- Broker Info Section End -->
        </div>
    </div>
    <?php
}  // Agent Section Only shows Branch Web Form Only.
?>

<div class="clear"></div>

<div class="pad5 left" style="width:48%;">
    <div class="block-content" style="width: 100%">
        <div class="head">Borrower Info</div>
        <table cellspacing="1" cellpadding="10" width="100%" border="0" class="clsLMSV">
            <?php if ($addBranchHearAbout == 1) { ?>
                <tr>
                    <td>How did you hear about us?</td>
                    <td>
                        <select name="branchLeadSource"
                                id="branchLeadSource" <?php if ($publicUser == '1' && $showLimitedMandatoryField == 0) { ?> class="mandatory" <?php } else { ?><?php } ?>
                                TABINDEX="<?php echo $tabIndex++ ?>"/>
                        <option value="">- Select -</option>
                        <?php
                        for ($bh = 0; $bh < count($branchHAInfoArray); $bh++) {
                            $branchHearAbout = '';
                            $sOpt = '';
                            $branchHearAbout = $branchHAInfoArray[$bh]['branchHearAbout'];
                            $sOpt = Arrays::isSelected($branchHearAbout, $leadSource);
                            ?>
                            <option <?php echo $sOpt; ?>
                                    value="<?php echo $branchHearAbout; ?>"><?php echo $branchHearAbout; ?></option>
                            <?php
                        }
                        ?>
                        </select>
                    </td>
                </tr>
            <?php } //  onchange="javascript:getChecklistMandatoryForWebForms('loanModForm', 'SV', 'HMLO', this.value);"
            if ($emailOpt != 'Email') {
                ?>
                <tr class="even">
                    <td>Loan Program?</td>
                    <td>
                        <div class="left" id="service_container">
                            <select data-placeholder="" name="LMRClientType[]" id="LMRClientType"
                                    tabindex="<?php echo $tabIndex++; ?>"
                                    onchange="allowToEditDisabledFields(this.value, 'disabledFields');getPCMinMaxLoanGuidelines('loanModForm', '<?php echo $PCID ?>');showAndHideLandFields(this.value); <?php //if (in_array($LMRId, $PCBasicLoanTabLMRIDsExists)) { } else { ?>populatePCBasicLoanInfo('loanModForm', this.value, '<?php echo $PCID ?>'); <?php //} ?>"
                                    class="chzn-select mandatory" style="width:240px;">
                                <option value="">- Select -</option>
                                <?php
                                $serviceCnt = 0;
                                if (count($servicesRequested) > 0) $serviceCnt = count($servicesRequested);
                                for ($j = 0; $j < $serviceCnt; $j++) {
                                    $LMRClientTypeCode = '';
                                    $sOpt = '';
                                    $LMRClientType = '';
                                    $chk = '';
                                    $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                    $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                    $chk = Strings::isKeyChecked($LMRClientTypeInfo, 'ClientType', $LMRClientTypeCode);
                                    if (trim($chk) == 'checked') $chk = 'selected ';
                                    if ($servicesRequested[$j]['internalLoanProgram'] == 0) {
                                        ?>
                                        <option <?php echo $chk; ?>
                                                value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                    <?php }
                                } ?>
                            </select>
                        </div>
                    </td>
                </tr>
            <?php } else { ?>
                <input type="hidden" name="LMRClientType[]" id="LMRClientType" value="<?php echo $ClientType; ?>">
            <?php } ?>
            <tr>
                <td>First Name</td>
                <td>
                    <input type="text" name="borrowerFName" class="mandatory" value="<?php echo $borrowerFName; ?>"
                           maxlength="30" autocomplete="off" TABINDEX="<?php echo $tabIndex++ ?>"/>
                </td>
            </tr>
            <tr class="even">
                <td>Last Name</td>
                <td>
                    <input type="text" name="borrowerLName" value="<?php echo $borrowerLName; ?>" class="mandatory"
                           maxlength="30" autocomplete="off" TABINDEX="<?php echo $tabIndex++ ?>"/>
                </td>
            </tr>
            <tr>
                <td>Entity Name (if applicable)</td>
                <td>
                    <input type="text" name="entityName" id="entityName" tabindex="<?php echo $tabIndex++; ?>"
                           value="<?php echo $entityName; ?>" size="40" maxlength="300" autocomplete="off">
                </td>
            </tr>
            <tr class="even">
                <td>Email</td>
                <td>
                    <div id="alert"></div>
                    <?php if ($emailOpt != 'Email') { ?>
                        <div class="left">
                            <input type="text" name="borrowerEmail" id="borrowerEmail"
                                   tabindex="<?php echo $tabIndex++; ?>" class="mandatory" maxlength="75" size="40"
                                   value="<?php echo $borrowerEmail; ?>" autocomplete="off">
                        </div>
                        <div id="showClientEmailExists" class="left"></div>
                        <div id="divLo1" class="left pad2" style="display:none;">
                            Please Wait... <img src="<?php echo CONST_SITE_URL; ?>assets/images/ajax-loader.gif"
                                                alt="">
                        </div>
                    <?php } else { ?>

                        <h5><?php echo $borrowerEmail ?></h5>
                        <input type="hidden" name="borrowerEmail" id="borrowerEmail"
                               value="<?php echo $borrowerEmail; ?>" maxlength="75" size="40">
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Primary Phone</td>
                <td>
                    <div id="alertphone"></div>
                    <div class="left">
                        <input type="hidden" name="phoneNumber" id="phoneNumber" value="">
                        <input type="text" name="phNo1" id="phNo1" tabindex="<?php echo $tabIndex++; ?>"
                               class="mandatory" maxlength="3" size="3" value="<?php echo $phNo1; ?>" autocomplete="off"
                               onkeyup="return autoTab('loanModForm', 'phNo1', 'phNo2');">
                        &nbsp;- &nbsp;<input type="text" name="phNo2" id="phNo2" tabindex="<?php echo $tabIndex++; ?>"
                                             class="mandatory" maxlength="3" size="3" value="<?php echo $phNo2; ?>"
                                             autocomplete="off"
                                             onkeyup="return autoTab('loanModForm', 'phNo2', 'phNo3');">
                        &nbsp;- &nbsp;<input type="text" name="phNo3" id="phNo3" tabindex="<?php echo $tabIndex++; ?>"
                                             class="mandatory" maxlength="4" size="4" value="<?php echo $phNo3; ?>"
                                             autocomplete="off">

                        <?php /*&nbsp; Ext #&nbsp;<input type="text" name="ext" id="ext" tabindex="<?php echo $tabIndex++;?>" maxlength="10" size="11" value="<?php echo $ext; ?>" autocomplete="off" >*/ ?>
                    </div>
                    <div id="showClientPhoneExists" class="left"></div>
                    <div id="divLo2" class="left pad2" style="display:none;">
                        Please Wait... <img src="<?php echo CONST_SITE_URL; ?>assets/images/ajax-loader.gif" alt="">
                    </div>
                </td>
            </tr>
            <tr class="even">
                <td>Date Of Birth</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <div class="left">
                            <input type="text" name="borrowerDOB" id="borrowerDOB" tabindex="<?php echo $tabIndex++; ?>"
                                   value="<?php echo $borrowerDOB; ?>" autocomplete="off" maxlength="10" size="12"><br>
                            <div>(MM/DD/YYYY)</div>
                        </div>
                        <div class="left pad2">
                            <a class="fa fa-calendar fa-2x borrowerDOB cursor icon-red" style="text-decordation:none;"
                               title="Click to open the calender"></a>
                        </div>

                    <?php } else { ?>
                        <h5><?php echo $borrowerDOB; ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Social Security Number</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <div class="left">
                            <input type="text" name="ssn1" id="ssn1" maxlength="3" size="3"
                                   tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $ssn1; ?>" autocomplete="off"
                                   onkeyup="return autoTab('loanModForm', 'ssn1', 'ssn2');">
                            &nbsp;- &nbsp;<input type="text" name="ssn2" id="ssn2" maxlength="2" size="2"
                                                 tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $ssn2; ?>"
                                                 autocomplete="off"
                                                 onkeyup="return autoTabSSN('loanModForm', 'ssn2', 'ssn3');">
                            &nbsp;- &nbsp;<input type="text" name="ssn3" id="ssn3" maxlength="4" size="3"
                                                 tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $ssn3; ?>"
                                                 autocomplete="off">
                        </div>
                        <div class="pad5 left with-children-tip">
                            <a class="fa fa-info-circle fa-2x tip-bottom" style="text-decoration:none;"
                               href="javascript:void(0);"
                               title="Social Security Numbers are not mandatory. For security purposes you do NOT have enter this data"></a>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo $ssnNumber; ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr class="even">
                <td>Credit Score Range</td>
                <td>
                    <select name="borCreditScoreRange" id="borCreditScoreRange" tabindex="<?php echo $tabIndex++; ?>">
                        <option value=''> - Select -</option>
                        <?php
                        for ($i = 0; $i < count($glHMLOCreditScoreRange); $i++) {
                            $sOpt = '';
                            $glCreditScoreRange = '';
                            $glCreditScoreRange = trim($glHMLOCreditScoreRange[$i]);
                            $sOpt = Arrays::isSelected($glCreditScoreRange, $borCreditScoreRange);
                            echo "<option value=\"" . $glCreditScoreRange . "\" " . $sOpt . '>' . $glCreditScoreRange . '</option>';
                        }
                        ?>
                    </select>
                </td>
            </tr>
            <tr>
                <td>Cell Number</td>
                <td>
                    <input type="text" name="cellNo1" id="cellNo1" maxlength="3" size="4"
                           tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $cellNo1; ?>"
                           onkeyup="return autoTab('loanModForm','cellNo1','cellNo2');" autocomplete="off"> -
                    <input type="text" name="cellNo2" id="cellNo2" maxlength="3" size="4"
                           value="<?php echo $cellNo2; ?>" tabindex="<?php echo $tabIndex++; ?>"
                           onkeyup="return autoTab('loanModForm','cellNo2','cellNo3');" autocomplete="off"> -
                    <input type="text" name="cellNo3" id="cellNo3" maxlength="4" size="4"
                           value="<?php echo $cellNo3; ?>" tabindex="<?php echo $tabIndex++; ?>" autocomplete="off">
                </td>
            </tr>
            <tr class="even">
                <td>Please select service provider<br> if you would like to receive<br> task reminders via SMS</td>
                <td>
                    <select name="serviceProvider"
                            id="serviceProvider" <?php if (in_array($processingCompanyId, $glMandatoryFieldForPC)) {
                        echo 'class="mandatory"';
                    } ?> tabindex="<?php echo $tabIndex++; ?>">
                        <option value=''> - Select -</option>
                        <?php
                        $SMSServiceProviderKeyArray = [];
                        $SMSServiceProviderKeyArray = array_keys($SMSServiceProviderArray);
                        for ($j = 0; $j < count($SMSServiceProviderKeyArray); $j++) {
                            $sOpt = '';
                            $servicePr = '';
                            $servicePr = trim($SMSServiceProviderKeyArray[$j]);
                            $sOpt = Arrays::isSelected($servicePr, $serviceProvider);
                            echo "<option value=\"" . trim($servicePr) . "\" " . $sOpt . '>' . trim($SMSServiceProviderArray[$servicePr]) . '</option>';
                        } ?>
                    </select>
                    <span class="pad2 with-children-tip">
								<a class="fa fa-info-circle fa-2x tip-right" style="text-decoration:none;"
                                   title='Please select service provider if you would like to receive task reminders via SMS.'></a>
							</span>
                </td>
            </tr>
        </table>
        <div style="display:table;width:100%">
            <div class="left" style="width:48%">Do you have Real Estate/Rehab/Construction Experience?</div>
            <div class="left" style="width:52%">
                <?php if ($allowToEdit) { ?>
                    <input type="radio" name="haveBorRehabConstructionExperience"
                           id="haveBorRehabConstructionExperienceYes" value="Yes"
                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $haveBorRehabConstructionExperience); ?>
                           onclick="showAndHideborrowerUnderExperience(this.value, 'borRehabConstructionExperienceDiv');">Yes
                    <input type="radio" name="haveBorRehabConstructionExperience"
                           id="haveBorRehabConstructionExperienceNo" value="No"
                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $haveBorRehabConstructionExperience); ?>
                           onclick="showAndHideborrowerUnderExperience(this.value, 'borRehabConstructionExperienceDiv');">No
                    <input type="radio" name="haveBorRehabConstructionExperience"
                           id="haveBorRehabConstructionExperienceNA" value="NA"
                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('NA', $haveBorRehabConstructionExperience); ?>
                           onclick="showAndHideborrowerUnderExperience(this.value, 'borRehabConstructionExperienceDiv');">NA
                <?php } else { ?>
                    <h5><?php echo $haveBorRehabConstructionExperience; ?></h5>
                <?php } ?>
            </div>
        </div>
        <div class="borRehabConstructionExperienceDiv" style="<?php echo $showRCDispOpt; ?>">
            <div style="display:table;width:100%">
                <div class="clear pad2"></div>
                <div class="left" style="width:48%"># of properties completed in last 24 months</div>
                <div class="left" style="width:52%">
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="borRehabPropCompleted" id="borRehabPropCompleted"
                               tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $borRehabPropCompleted; ?>"
                               size="10" maxlength="10" autocomplete="off">
                    <?php } else { ?>
                        <h5><?php echo $borRehabPropCompleted; ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>
        <div class="even" style="display:table;width:100%">
            <div class="left landValueCls" style="width:48%;<?php echo $landValueCls ?>">Do you own the land?</div>
            <div class="left landValueCls" style="width:52%;<?php echo $landValueCls ?>">
                <?php if ($allowToEdit) { ?>
                    <input type="radio" name="isOwnLand" id="isOwnLandYes" value="Yes"
                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $isOwnLand); ?>>Yes
                    <input type="radio" name="isOwnLand" id="isOwnLandNo" value="No"
                           tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $isOwnLand); ?>>No
                <?php } else { ?>
                    <h5><?php echo $isOwnLand; ?></h5>
                <?php } ?>
            </div>
        </div>
    </div>
</div>
<!-- Borrower's Present Address Section Start -->
<div class="right pad5" style="width: 48%">
    <div class="block-content" style="width:100%">
        <div class="head">Borrower Address</div>
        <table style="width:100%" border="0">
            <tr>
                <td>Address</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="presentAddress" id="presentAddress"
                               tabindex="<?php echo $tabIndex++; ?>" value="<?php echo $presentAddress ?>" size="40"
                               maxlength="75" autocomplete="off">
                    <?php } else { ?>
                        <h5><?php echo $presentAddress ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr class="even">
                <td>City</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="presentCity" id="presentCity" value="<?php echo $presentCity ?>"
                               tabindex="<?php echo $tabIndex++; ?>" size="20" maxlength="30" autocomplete="off">
                    <?php } else { ?>
                        <h5><?php echo $presentCity ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>State</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <select name="presentState" id="presentState" tabindex="<?php echo $tabIndex++; ?>">
                            <option value=''> - Select -</option>
                            <?php
                            for ($j = 0; $j < count($stateArray); $j++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $presentState);
                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $presentState ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr class="even">
                <td>Zip Code</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="presentZip" id="presentZip" tabindex="<?php echo $tabIndex++; ?>"
                               value="<?php echo $presentZip ?>" size="10" maxlength="10" autocomplete="off"><br>
                    <?php } else { ?>
                        <h5><?php echo $presentZip ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Has the borrower resided at the present address for less than two years?</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <input type="radio" name="borResidedPresentAddr" value="Yes"
                               tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('Yes', $borResidedPresentAddr); ?>
                               onclick="showFormerAddrDiv(this.value, 'Bor');">Yes
                        <input type="radio" name="borResidedPresentAddr" value="No"
                               tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('No', $borResidedPresentAddr); ?>
                               onclick="showFormerAddrDiv(this.value, 'Bor');">No
                        <!-- <input type="radio" name="borResidedPresentAddr"  value="NA" tabindex="<?php echo $tabIndexNo++; ?>" <?php echo Strings::isChecked('NA', $borResidedPresentAddr); ?> onclick="javascript:showFormerAddrDiv(this.value, 'Bor');">NA -->
                    <?php } else { ?>
                        <h5><?php echo $borResidedPresentAddr; ?></h5>
                    <?php } ?>
                </td>
            </tr>
        </table>
    </div><!-- Previous Address Section End -->
</div>
<!-- Borrower's Present Address Section End -->
<!-- Property Details Section Start -->
<div class="pad5 right" style="width:48%;">
    <div class="block-content" style="width:100%;">
        <div class="head">Property Details</div>
        <table width="100%" border="0" class="disabledFields">
            <tr>
                <td>Where are you in the process?</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <select name="propDetailsProcess" id="propDetailsProcess" tabindex="<?php echo $tabIndex++; ?>">
                            <option value=''> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($glPropDetailsProcess); $i++) {
                                $sOpt = '';
                                $propDetals = '';
                                $propDetals = trim($glPropDetailsProcess[$i]);
                                $sOpt = Arrays::isSelected($propDetals, $propDetailsProcess);
                                echo "<option value=\"" . $propDetals . "\" " . $sOpt . '>' . $propDetals . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $propDetailsProcess ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Address</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="propertyAddress" id="propertyAddress" class="mandatory"
                               tabindex="<?php echo $tabIndex++; ?>"
                               value="<?php echo Strings::showField('propertyAddress', 'LMRInfo') ?>" size="40"
                               maxlength="75"
                               autocomplete="off">
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('propertyAddress', 'LMRInfo') ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr class="even">
                <td>City</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="propertyCity" id="propertyCity" class="mandatory"
                               value="<?php echo Strings::showField('propertyCity', 'LMRInfo') ?>"
                               tabindex="<?php echo $tabIndex++; ?>" size="20" maxlength="30" autocomplete="off">
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('propertyCity', 'LMRInfo') ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>State</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <select name="propertyState" id="propertyState" class="mandatory"
                                tabindex="<?php echo $tabIndex++; ?>">
                            <option value=''> - Select -</option>
                            <?php
                            for ($j = 0; $j < count($stateArray); $j++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), Strings::showField('propertyState', 'LMRInfo'));
                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('propertyState', 'LMRInfo') ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr class="even">
                <td>Zip Code</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="propertyZip" id="propertyZip" class="mandatory"
                               tabindex="<?php echo $tabIndex++; ?>"
                               value="<?php echo Strings::showField('propertyZip', 'LMRInfo') ?>" size="10"
                               maxlength="10"
                               autocomplete="off"><br>
                    <?php } else { ?>
                        <h5><?php echo Strings::showField('propertyZip', 'LMRInfo') ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Present Occupancy</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <select name="presentOccupancy" id="presentOccupancy" tabindex="<?php echo $tabIndex++; ?>">
                            <option value=''> - Select -</option>
                            <?php
                            for ($i = 0; $i < count($glHMLOPresentOccupancy); $i++) {
                                $sOpt = '';
                                $typeOfHouse = '';
                                $typeOfHouse = trim($glHMLOPresentOccupancy[$i]);
                                $sOpt = Arrays::isSelected($typeOfHouse, $presentOccupancy);
                                echo "<option value=\"" . $typeOfHouse . "\" " . $sOpt . '>' . $typeOfHouse . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo $presentOccupancy ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Property Type</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <select name="propertyType" id="propertyType" TABINDEX="<?php echo $tabIndex++; ?>">
                            <option value=""> - Select -</option>
                            <?php
                            $propertyTypeKeyArray = [];
                            if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray) > 0) {
                                $propertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
                            }
                            for ($o = 0; $o < count($propertyTypeKeyArray); $o++) {
                                $propKey = $propertyTypeKeyArray[$o];
                                $propVal = GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertyTypeKeyArray[$o]];
                                $sOpt = Arrays::isSelected($propKey, Strings::showField('propertyType', 'LMRInfo'));
                                $opt = "<option value=\"" . $propKey . "\"  " . $sOpt . '>' . $propVal . "</option>\n";
                                echo $opt;
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5>
                            <?php
                            if (array_key_exists(Strings::showField('propertyType', 'LMRInfo'), GpropertyTypeNumbArray::$GpropertyTypeNumbArray)) {
                                echo GpropertyTypeNumbArray::$GpropertyTypeNumbArray[Strings::showField('propertyType', 'LMRInfo')];
                            }
                            ?>
                        </h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Property Condition</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <select name="propertyCondition" id="propertyCondition" TABINDEX="<?php echo $tabIndex++; ?>">
                            <option value=""> - Select -</option>
                            <?php
                            for ($pc = 0; $pc < count($glPropertyCondition); $pc++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected($glPropertyCondition[$pc], Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyCondition);
                                echo "<option value=\"" . trim($glPropertyCondition[$pc]) . "\" " . $sOpt . '>' . trim($glPropertyCondition[$pc]) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo Property::$primaryPropertyInfo->getTblPropertiesDetails_by_propertyId()->propertyCondition; ?></h5>
                    <?php } ?>
                </td>
            </tr>
        </table>
    </div>
</div>
<!-- Subject Property Info Section End -->
<div class="clear pad2"></div>
<!-- Assets Section Start -->
<div class="pad5" style="width:98%;">
    <div class="block-content" style="width:100%;">
        <div class="head">Assets</div>
        <table width="100%" border="0">
            <tr>
                <td>Checking:</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="assetCheckingAccounts" id="assetCheckingAccounts"
                                      onblur="currencyConverter(this, this.value);calculateTotalAssets(this.value);"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($assetCheckingAccounts) ?>"
                                      size="14" maxlength="40" tabindex="<?php echo $tabIndex++; ?>"
                                      autocomplete="off"/>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($assetCheckingAccounts) ?></h5>
                    <?php } ?>
                </td>
                <td>Saving:</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="assetSavingMoneyMarket" id="assetSavingMoneyMarket"
                                      onblur="currencyConverter(this, this.value);calculateTotalAssets(this.value);"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($assetSavingMoneyMarket) ?>"
                                      size="14" maxlength="40" tabindex="<?php echo $tabIndex++; ?>"
                                      autocomplete="off"/>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($assetSavingMoneyMarket) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr class="even">
                <td>Stocks / Bonds / CDs</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="assetStocks" id="assetStocks"
                                      onblur="currencyConverter(this, this.value);calculateTotalAssets(this.value);"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($assetStocks) ?>"
                                      size="14"
                                      maxlength="40" tabindex="<?php echo $tabIndex++; ?>"
                                      autocomplete="off"/>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($assetStocks) ?></h5>
                    <?php } ?>
                </td>
                <td>IRA</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="assetIRAAccounts" id="assetIRAAccounts"
                                      onblur="currencyConverter(this, this.value);calculateTotalAssets(this.value);"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($assetIRAAccounts) ?>"
                                      size="14"
                                      maxlength="40" tabindex="<?php echo $tabIndex++; ?>" autocomplete="off"
                                      />
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($assetIRAAccounts) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>401k</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="assetESPOAccounts" id="assetESPOAccounts"
                                      onblur="currencyConverter(this, this.value);calculateTotalAssets(this.value);"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($assetESPOAccounts) ?>"
                                      size="14"
                                      maxlength="40" tabindex="<?php echo $tabIndex++; ?>" autocomplete="off"
                                      />
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($assetESPOAccounts) ?></h5>
                    <?php } ?>
                </td>
                <td><h5>Total Assets</h5></td>
                <td><h3>$
                        <span id="totalAssetsDisp"><?php echo Currency::formatDollarAmountWithDecimal($totalAssets) ?></span>
                    </h3></td>
            </tr>
        </table>
    </div>
</div>
<div class="clear pad2"></div>
<!-- Assets Section End -->

<?php
if (count($servicesRequested) > 0) {
    if (array_key_exists($executiveId, $servicesRequested)) $servicesRequested = $servicesRequested[$executiveId];
}

require 'HMLOLoanTermsForm.php'; /** Fees And Costs Section. **/
?>
<div class="pad5 left refinanceSection" style="width:48%; <?php echo $refinanceSectionDispOpt; ?>">
    <div class="block-content" style="width: 100%">
        <div class="head">Refinance Current Mortgage</div>
        <table width="100%" border="0">
            <tr>
                <td>Original Purchase Date</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        <div class="left"> &nbsp;&nbsp;
                            <input type="text" name="originalPurchaseDate" id="originalPurchaseDate"
                                   value="<?php echo $originalPurchaseDate ?>" size="10"
                                   class="dateNewClass"
                                   TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off">
                            <br> &nbsp;&nbsp;&nbsp;&nbsp;(MM/DD/YYYY)
                        </div>
                        <div class="left pad2">
                            <a class="fa fa-calendar fa-2x originalPurchaseDate icon-red cursor"
                               style="text-decoration:none;" title="Click to open the calender"></a>
                        </div>
                    <?php } else { ?>
                        <h5><?php echo $originalPurchaseDate ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Current Lender</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        &nbsp;&nbsp;&nbsp;<input type="text" name="refinanceCurrentLender" id="refinanceCurrentLender"
                                                 value="<?php echo $refinanceCurrentLender ?>" autocomplete="off"
                                                 maxlength="50" tabindex="<?php echo $tabIndex++; ?>" size="45"
                                                 placeholder=" - Type Name Here - " style="width:200px;">
                    <?php } else { ?>
                        <h5><?php echo $refinanceCurrentLender; ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Original Purchase Price</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="originalPurchasePrice" id="originalPurchasePrice"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($originalPurchasePrice) ?>"
                                      onblur="currencyConverter(this, this.value);"
                                      size="20"
                                      maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                    <?php } else { ?>
                        <h5>$ <?php echo Currency::formatDollarAmountWithDecimal($originalPurchasePrice) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Cost of Improvements made</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="costOfImprovementsMade" id="costOfImprovementsMade"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($costOfImprovementsMade) ?>"
                                      onblur="currencyConverter(this, this.value);"
                                      size="20"
                                      maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                    <?php } else { ?>
                        <h5>$ <?php echo Currency::formatDollarAmountWithDecimal($costOfImprovementsMade) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Monthly Payment</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="refinanceMonthlyPayment" id="refinanceMonthlyPayment"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($refinanceMonthlyPayment) ?>"
                                      onblur="currencyConverter(this, this.value);"
                                      size="20"
                                      maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                    <?php } else { ?>
                        <h5>$ <?php echo Currency::formatDollarAmountWithDecimal($refinanceMonthlyPayment) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Current Rate</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        %<input type="text" name="refinanceCurrentRate" id="refinanceCurrentRate"
                                value="<?php echo Currency::formatRateAmountWithDecimal($refinanceCurrentRate); ?>"
                                size="20"
                                maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatRateAmountWithDecimal($refinanceCurrentRate) ?> %</h5>
                    <?php } ?>
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- Subject Property Info Section -->
<div class="pad5 right subjectPropertySection" style="width:48%; <?php echo $subjectPropertySectionDispOpt; ?>">
    <div class="block-content">
        <div class="head">Subject Property Cash Flow (Annualized)</div>
        <table style="width:100%" border="0">
            <tr>
                <td width="68%">Gross Potential Income</td>
                <td width="32%">
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="actualRentsInPlace" id="actualRentsInPlace"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($actualRentsInPlace) ?>"
                                      onblur="currencyConverter(this, this.value);calculateNetOperatingIncome('loanModForm', 'totalNetOperatingIncome');"
                                      size="18"
                                      maxlength="68"
                                      TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($actualRentsInPlace) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Operating Expenses
                    <div class="note">Do not include mortgage payment or depreciation as part of Actual Expenses.</div>
                </td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="lessActualExpenses" id="lessActualExpenses"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($lessActualExpenses) ?>"
                                      onblur="currencyConverter(this, this.value);calculateNetOperatingIncome('loanModForm', 'totalNetOperatingIncome');"
                                      size="18"
                                      maxlength="68"
                                      TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($lessActualExpenses) ?></h5>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Net Operating Income</td>
                <td><h5 id="totalNetOperatingIncome">
                        $ <?php echo Currency::formatDollarAmountWithDecimal($netOperatingIncome) ?></h5></td>
            </tr>
            <tr>
                <td>Gross Annual Rent of Largest Tenant</td>
                <td>
                    <?php if ($allowToEdit) { ?>
                        $&nbsp;<input type="text" name="grossAnnualRentLargestTenant" id="grossAnnualRentLargestTenant"
                                      value="<?php echo Currency::formatDollarAmountWithDecimal($grossAnnualRentLargestTenant) ?>"
                                      onblur="currencyConverter(this, this.value);"
                                      size="18"
                                      maxlength="68" TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"/>
                    <?php } else { ?>
                        <h5><?php echo Currency::formatDollarAmountWithDecimal($grossAnnualRentLargestTenant) ?></h5>
                    <?php } ?>
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="clear pad2"></div>

<div class="pad5" style="width:80%; margin: auto;"><h3>Borrower notes, questions, or special circumstances</h3><textarea
            rows="5" name="mortgageNotes" id="mortgageNotes"
            cols="100"><?php echo Strings::showField('mortgageNotes', 'LMRInfo') ?></textarea>
    <div class="clear"></div>
    <h3>Terms And Conditions</h3>
    <textarea readonly rows="12" cols="100"><?php echo $HMLOTAC; ?></textarea><br>
    <div style="text-align: center;"><input type="checkbox" id="agreeTC" name="agreeTC"
                                            value="1" <?php if ($LMRId > 0) echo 'Checked'; ?> class="mandatory"> By
        checking this box I agree to the terms and conditions.
    </div>
</div>

<table cellspacing="1" cellpadding="10" width="100%" border="0" class="clsLMSV">
    <tr>
        <td colspan="4" style="text-align:center">
            <input type="submit" class="button" name="btnSave" value="Save & Next"
                   tabindex="<?php echo $tabIndex++; ?>">&nbsp;
        </td>
    </tr>
</table>

