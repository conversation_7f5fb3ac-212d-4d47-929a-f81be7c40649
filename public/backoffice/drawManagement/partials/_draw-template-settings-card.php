    <div class="card-body drawTemplateSettings collapse">
        <form name="drawTemplateSettingsForm" id="drawTemplateSettingsForm" action="#">
            <input type="hidden" name="pcid" value="<?= $PCID ?>">
            <div class="col-md-12 mb-4">
                <div class="card card-custom">
                    <div class="card-header card-header-tabs-line bg-gray-100">
                        <div class="card-title">
                            <h3 class="card-label">
                                Feature Settings
                            </h3>
                        </div>
                        <div class="card-toolbar">
                            <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass collapsed" data-card-tool="toggle" data-section="drawTemplateSettings" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                                <i class="ki icon-nm ki-arrow-down"></i>
                            </a>
                        </div>
                    </div>

                    <div class="card-body collapse">
                        <div class="form-group row align-items-center">
                            <label class="col-lg-4">Enable Simple Mode
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This mode will allows portal users to simply input a Draw History Table with the following fields: Status, Submission Date, Amount Requested, Amount Approved, Wire Amount, Wire Sent Date.">
                                </i>
                            </label>
                            <div class="col-lg-2">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" type="checkbox"
                                        value="" id="enableSimpleModeTog" onchange="toggleSwitch('enableSimpleModeTog','enableSimpleMode','1','0' );"/>
                                        <span></span>
                                        <input type="hidden" name="enableSimpleMode" id="enableSimpleMode" value="0">
                                    </label>
                                </span>
                            </div>
                        </div>

                        <div class="form-group row align-items-center">
                            <label class="col-lg-4">Allow Users to Delete Uploads
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This will allow your backoffice users the ability to delete attachments provided by themselves or the borrower.">
                                </i>
                            </label>
                            <div class="col-lg-2">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" type="checkbox"
                                        value="" id="allowUsersDeleteUploadsTog" onchange="toggleSwitch('allowUsersDeleteUploadsTog','allowUsersDeleteUploads','1','0');"/>
                                        <span></span>
                                        <input type="hidden" name="allowUsersDeleteUploads" id="allowUsersDeleteUploads" value="0">
                                    </label>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 mb-4">
                <div class="card card-custom">
                    <div class="card-header card-header-tabs-line bg-gray-100">
                        <div class="card-title">
                            <h3 class="card-label">
                                Template Settings
                            </h3>
                        </div>
                        <div class="card-toolbar">
                            <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass collapsed" data-card-tool="toggle" data-section="drawTemplateSettings" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                                <i class="ki icon-nm ki-arrow-down"></i>
                            </a>
                        </div>
                    </div>

                    <div class="card-body collapse">
                        <div class="form-group row align-items-center">
                            <label class="col-lg-4">Allow Borrowers to Add / Edit Categories?
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This permission allows borrower's to Add or Edit categories to a scope of work.">
                                </i>
                            </label>

                            <div class="col-lg-2">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" type="checkbox"
                                        value="" id="allowBorrowersAddEditCategoriesTog" onchange="toggleSwitch('allowBorrowersAddEditCategoriesTog','allowBorrowersAddEditCategories','1','0');"/>
                                        <span></span>
                                        <input type="hidden" name="allowBorrowersAddEditCategories" id="allowBorrowersAddEditCategories" value="0">
                                    </label>
                                </span>
                            </div>
                        </div>

                        <div class="form-group row align-items-center">
                            <label class="col-lg-4">Allow Borrowers to Remove Your Template's Categories?
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This permission will allow a borrower to Remove any categories from your preset scope of work. Recommended to be turned off if attempting to enforce a uniform budget across your borrowers and loan files.">
                                </i>
                            </label>
                            <div class="col-lg-2">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" type="checkbox"
                                        value="" id="allowBorrowersDeleteCategoriesTog" onchange="toggleSwitch('allowBorrowersDeleteCategoriesTog','allowBorrowersDeleteCategories','1','0' );"/>
                                        <span></span>
                                        <input type="hidden" name="allowBorrowersDeleteCategories" id="allowBorrowersDeleteCategories" value="0">
                                    </label>
                                </span>
                            </div>
                        </div>

                        <div class="form-group row align-items-center">
                            <label class="col-lg-4">Allow Borrowers to Add / Edit Line Items?
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This permission will allow a borrower to Add or Edit line items from your preset scope of work. Recommended to be turned off if attempting to enforce a uniform budget across your borrowers and loan files.">
                                </i>
                            </label>
                            <div class="col-lg-2">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" type="checkbox"
                                        value="" id="allowBorrowersAddEditLineItemsTog" onchange="toggleSwitch('allowBorrowersAddEditLineItemsTog','allowBorrowersAddEditLineItems','1','0' );"/>
                                        <span></span>
                                        <input type="hidden" name="allowBorrowersAddEditLineItems" id="allowBorrowersAddEditLineItems" value="0">
                                    </label>
                                </span>
                            </div>
                        </div>

                        <div class="form-group row align-items-center">
                            <label class="col-lg-4">Allow Borrowers to Remove Your Template's Line Items?
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This permission will allow a borrower to Remove line items from your preset scope of work. Recommended to be turned off if attempting to enforce a uniform budget across your borrowers and loan files.">
                                </i>
                            </label>
                            <div class="col-lg-2">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" type="checkbox"
                                        value="" id="allowBorrowersDeleteLineItemsTog" onchange="toggleSwitch('allowBorrowersDeleteLineItemsTog','allowBorrowersDeleteLineItems','1','0' );"/>
                                        <span></span>
                                        <input type="hidden" name="allowBorrowersDeleteLineItems" id="allowBorrowersDeleteLineItems" value="0">
                                    </label>
                                </span>
                            </div>
                        </div>

                        <div class="form-group row align-items-center">
                            <label class="col-lg-4">Draw Fee
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This amount will automatically deduct from any approved amounts on a draw, as to determine the final wire amount.">
                                </i>
                            </label>
                            <div class=col-lg-4>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" id="inputGroup-sizing-default">$</span>
                                    </div>
                                    <input type="text" name="drawFee" id="drawFee" class="form-control" aria-label="Draw Fees" aria-describedby="inputGroup-sizing-default" placeholder="0.00">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 mb-4">
                <div class="card card-custom">
                    <div class="card-header card-header-tabs-line bg-gray-100">
                        <div class="card-title">
                            <h3 class="card-label">
                                Revision Settings
                            </h3>
                        </div>
                        <div class="card-toolbar">
                            <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass collapsed" data-card-tool="toggle" data-section="drawTemplateSettings" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                                <i class="ki icon-nm ki-arrow-down"></i>
                            </a>
                        </div>
                    </div>

                    <div class="card-body collapse">
                        <div class="form-group row align-items-center">
                            <label class="col-lg-4">Allow Borrowers to Submit for Scope of Work Revisions?
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This permission will allow a borrower to request a revision to their originally submitted scope of work.">
                                </i>
                            </label>
                            <div class="col-lg-2">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" type="checkbox"
                                        value="" id="allowBorrowersSOWRevisionsTog" onchange="toggleSwitch('allowBorrowersSOWRevisionsTog','allowBorrowersSOWRevisions','1','0' );"/>
                                        <span></span>
                                        <input type="hidden" name="allowBorrowersSOWRevisions" id="allowBorrowersSOWRevisions" value="0">
                                    </label>
                                </span>
                            </div>
                        </div>

                        <div class="form-group row align-items-center exceedRehabCostDiv">
                            <label class="col-lg-4">Allow Borrowers to Request More Than Rehab Cost Financed During a Revision?
                                <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                                title="This permission will allow a borrower to request more then the funded amount when revising their budget. Recommended to be turned off.">
                                </i>
                            </label>
                            <div class="col-lg-2">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" type="checkbox"
                                        value="" id="allowBorrowersExceedFinancedRehabCostOnRevisionTog" onchange="toggleSwitch('allowBorrowersExceedFinancedRehabCostOnRevisionTog','allowBorrowersExceedFinancedRehabCostOnRevision','1','0' );"/>
                                        <span></span>
                                        <input type="hidden" name="allowBorrowersExceedFinancedRehabCostOnRevision" id="allowBorrowersExceedFinancedRehabCostOnRevision" value="0">
                                    </label>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <div class="d-flex justify-content-center mt-5">
            <button type="submit" class="btn btn-primary save-settings ml-2">Save</button>
        </div>
        </form>
    </div>
