<?php
global $URLPOSTING, $activeTab, $borrowerEmail, $docArray, $userTimeZone, $oldFPCID, $LMRId,
       $PCID, $propertyValuationDocInfo,
       $userGroup, $SID, $isHMLOSelOpt, $assetId, $paymentReserves, $lien1Payment, $totalInsurance,
       $cliType, $isHOALien, $QAID, $allowToSeeBillingSectionForFile, $borrowerActiveSectionDisp,
       $publicUser, $emailOpt, $allowToEdit, $webFormFOpt,
       $loanOfficerId, $executiveId, $HMLORealEstateTaxes, $oBroker,
       $fieldsInfo, $tabIndex, $REBroker, $REBrokerYesBtn, $agentNumber, $brokerFName,
       $brokerLName, $brokerEmail, $LmbInfo, $brokerCompany, $brokerPhone, $RELoanofficer,
       $RELoanofficerYesBtn, $loanofficerFName, $loanofficerLName, $loanofficerEmail,
       $loanofficerCompany, $LoanofficerPhone, $LMRClientTypeDisplay, $PCBasicLoanTabLMRIDsExists,
       $ft, $servicesRequested, $LMRClientTypeInfo, $fileLP, $loanInfoLPSectionDisp,
       $myFileInfo, $propDetailsProcess, $branchHAInfoArray, $leadSource, $hereAbout,
       $referringParty, $hideThisField, $userRole, $isBlanketLoanDiv,
       $isBlanketLoan, $isBlanketLoanDispOpt, $noOfPropertiesAcquiring, $HMLOLoanInfoSectionsDisp,
       $HMLOTAC, $HMLOTACQA, $wfOpt, $aud, $allowCaptcha, $isPLO, $loanPgmDetails;

use models\composite\oBroker\listAllAgentsLoanOfficer;
use models\composite\oBroker\getBranchBrokerList;
use models\constants\gl\glAgentLabelChanges;
use models\constants\gl\glAllowHMLOPCToEditBrokerINLV;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\globalSBALoanProductsCat;
use models\constants\gl\glPropDetailsProcess;
use models\Controllers\loanForm;
use models\cypher;
use models\standard\BaseHTML;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\composite\oPC\getPCInternalServiceType;

$globalSBALoanProductsCat = globalSBALoanProductsCat::$globalSBALoanProductsCat;
$glPropDetailsProcess = glPropDetailsProcess::$glPropDetailsProcess;
$glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;
$glAgentLabelChanges = glAgentLabelChanges::$glAgentLabelChanges;

if ($URLPOSTING == 1) {
    require 'webformInitVars.php';
} else {
    require 'HMLOLoanInfoVars.php';
}
global $fileTab;
$fileTab = '1003';

$borrowerEmailLink = '';

$recordDate = Strings::showField('recordDate', 'LMRInfo');

if (isset($_REQUEST['borrowerEmail'])) $borrowerEmailLink = trim($_REQUEST['borrowerEmail']);

if ($borrowerEmailLink != '') {
    $borrowerEmail = $borrowerEmailLink;
} else {
    if (!$borrowerEmail) {
        $borrowerEmail = Strings::showField('borrowerEmail', 'LMRInfo');
    }

}

/**
 * Desc : Proof of sale (HUD)
 * Date : 09 Mar, 2017
 */

$docCnt = 1;
$proofOfSale1 = '';
$proofOfSale2 = '';
$proofOfSale3 = '';
$proofOfSale4 = '';
$proofOfSale5 = '';
$proofOfSale6 = '';
$proofOfSale7 = '';
$proofOfSale8 = '';
$proofOfSale9 = '';
$proofOfSale10 = '';
$proofOfSale11 = '';
$proofOfSale12 = '';
$proofOfSale13 = '';
$proofOfSale14 = '';
$proofOfSale15 = '';
$hudDocN7 = '';
$hudDocN8 = '';
$hudDocN9 = '';
$hudDocN10 = '';
$hudDocN11 = '';
$hudDocN12 = '';
$hudDocN13 = '';
$hudDocN14 = '';
$hudDocN15 = '';
$fileUpdLimit = 5;
for ($doc = 0; $doc < count($docArray); $doc++) {
    $tempDocArray = [];
    $docCategoryArray = [];
    $flatNotes = '';
    $uploadDocUrl = '';
    $docName = '';
    $displayDocName = '';
    $docId = 0;
    $myUploadedBy = '';
    $myUploadedRole = '';
    $docCategory = '';

    $docName = trim($docArray[$doc]['docName']);
    $displayDocName = trim($docArray[$doc]['displayDocName']);
    $uploadedDate = trim($docArray[$doc]['uploadedDate']);
    $userId = trim($docArray[$doc]['uploadedBy']);
    $userType = trim($docArray[$doc]['uploadingUserType']);
    $docCategory = trim($docArray[$doc]['docCategory']);
    $docId = trim($docArray[$doc]['docID']);
    $fileType = trim($docArray[$doc]['fileType']);

    $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
    $ipArray['outputZone'] = $userTimeZone;
    $ipArray['inputTime'] = $uploadedDate;
    $uploadedDate = Dates::timeZoneConversion($ipArray);

    $docCategoryArray = explode('-', $docCategory);

    if (count($docCategoryArray) > 0) {
        if ($docCategoryArray[0] == 'Proof of sale (HUD)') {

            if (Dates::IsEmpty($uploadedDate)) {
                $uploadedDate = '';
            } else {
                $uploadedDate = Dates::formatDateWithRE($uploadedDate, 'YMD_HMS', 'm/d/Y h:i A');
            }

            if ($displayDocName == '' || $displayDocName == NULL) {
                $displayDocName = $docName;
            }

            $tempRecDate = str_replace('-', '', $recordDate);
            $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
            $fileValue = $LMRId;

            $fP = $folderName . '/' . $fileValue . '/upload';
            $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
            if (isset($docCategoryArray[1])) ${'proofOfSale' . $docCategoryArray[1]} = $uploadDocUrl;
            if (isset($docCategoryArray[1])) ${'hudDocN' . $docCategoryArray[1]} = $displayDocName;
        }
    }
}
if (in_array($PCID, $glFirstRehabLending)) {
    unset($glPropDetailsProcess[2]);
    $glPropDetailsProcess = array_values($glPropDetailsProcess);
}

$titlereportDoc = '';
$pc = 1;

if (count($propertyValuationDocInfo) > 0) {
    $keysArray = [];
    $keysArray = array_keys($propertyValuationDocInfo);
    for ($i = 0; $i < count($keysArray); $i++) {
        $docCategory = '';
        $uploadDocUrl = '';
        $docName = '';
        $displayDocName = '';
        $fileType = '';
        $docID = '';
        $uploadedBy = '';
        $docCategory = trim($keysArray[$i]);
        $docName = trim($propertyValuationDocInfo[$docCategory]['docName']);
        $displayDocName = trim($propertyValuationDocInfo[$docCategory]['displayDocName']);
        $fileType = trim($propertyValuationDocInfo[$docCategory]['fileType']);
        $docID = trim($propertyValuationDocInfo[$docCategory]['docID']);
        $uploadedBy = trim($propertyValuationDocInfo[$docCategory]['uploadedBy']);

        $tempRecDate = str_replace('-', '', $recordDate);
        $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
        $fileValue = $LMRId;

        $fP = $folderName . '/' . $fileValue . '/property';
        ${strtolower(str_replace(' ', '', $docCategory)) . 'Doc'} = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) . '&opt=enc';
        ${strtolower(str_replace(' ', '', $docCategory)) . 'DocName'} = substr($displayDocName, 0, 18) . '.' . $fileType;
    }
    $proInsCnt = $pc;
}
if ($userGroup == 'Client') {
    $disableFld = '';
}
?>
<input type="hidden" name="submitType" value="Submit">
<input type="hidden" name="SID" value="<?php echo $SID; ?>">
<input type="hidden" name="UGroup" value="<?php echo $userGroup; ?>">
<input type="hidden" name="isHMLOOpt" id="isHMLOOpt" value="<?php echo $isHMLOSelOpt; ?>">
<input type="hidden" name="assetId" value="<?php echo $assetId; ?>">
<input type="hidden" name="paymentReserves" value="<?php echo $paymentReserves; ?>">
<input type="hidden" name="HMLORealEstateTaxes" id="HMLORealEstateTaxes" value="<?php echo $HMLORealEstateTaxes; ?>">
<input type="hidden" name="totalInsurance" value="<?php echo $totalInsurance; ?>">
<input type="hidden" name="mortgage1MonthlyPayment" value="<?php echo $lien1Payment; ?>">
<input type="hidden" name="cliType" id="cliType" value="<?php echo $cliType; ?>">
<input type="hidden" name="recordDate" id="recordDate"
       value="<?php echo Strings::showField('recordDate', 'LMRInfo') ?>"/>
<input type="hidden" name="isHOALien" id="isHOALien" value="<?php echo $isHOALien ?>"/>
<input type="hidden" name="fileUpdLimit" id="fileUpdLimit" value="<?php echo $fileUpdLimit ?>"/>
<input type="hidden" name="QAID" id="QAID" value="<?php echo $QAID; ?>"/>
<input type="hidden" name="RUID" value="<?php echo Strings::showField('RUID', 'billingUrlInfo') ?>">
<input type="hidden" name="allowToSeeBillingSectionForFile" id="allowToSeeBillingSectionForFile"
       value="<?php echo $allowToSeeBillingSectionForFile ?>">
<input type="hidden" name="inPro" id="inPro" value="<?php if ($borrowerActiveSectionDisp == '') {
    echo 'hide';
} ?>">

<?php
$lastUpdatedParam = '';
$fileRow = '';
if ($LMRId == 0) { //automation for new file
    $isFssUpdated = 'Yes';
    $fileRow = 'Insert';
} else {
    $isFssUpdated = 'No';
    $fileRow = 'Update';
}
if ($publicUser) $lastUpdatedParam = 'PFS';
?>
<!-- automation -->
<input type="hidden" name="isFssUpdated" id="isFssUpdated" value="<?php echo $isFssUpdated; ?>">
<input type="hidden" name="lastUpdatedParam" id="lastUpdatedParam" value="<?php echo $lastUpdatedParam; ?>">
<input type="hidden" name="lastUpdatedFss" id="lastUpdatedFss" value="">
<!-- // automation // -->
<input type="hidden" name="fileRow" id="fileRow" value="<?php echo $fileRow; ?>">
<?php
if ($publicUser != 1) { // Not allow this section for public users
    require 'fileAdminInfo.php';
} else { ?>
    <input type="hidden" name="selectedPC" id="selectedPC" value="<?php echo $PCID; ?>">

    <?php
    if ($PCID == 4326 && $LMRId == 0) { // **exception** PCID = 4326 (BD Capital)
        $internalLoanProgramList = [];
        $ip = [
            'PCID'       => $PCID,
            'keyNeeded'  => 'n',
            'moduleCode' => 'HMLO',
        ];
        $internalLoanProgramList = getPCInternalServiceType::getReport($ip);
        $serviceCnt = 0;
        if (count($internalLoanProgramList) > 0) $serviceCnt = count($internalLoanProgramList);
        for ($a = 0; $a < $serviceCnt; $a++) {
            $LMRClientTypeCode = '';
            $LMRClientType = '';
            $LMRClientTypeCode = trim($internalLoanProgramList[$a]['LMRClientType']);
            $LMRClientType = trim($internalLoanProgramList[$a]['serviceType']);
            if ($internalLoanProgramList[$a]['internalLoanProgram'] == '1' && $LMRClientType == 'To Be Determined') { ?>
                <input type="hidden" name="LMRInternalLoanProgram[]" class="" value="<?php echo $LMRClientTypeCode; ?>">
                <?php
            }
        }
    }
}

/**
 *
 * Customization for PC = Lendterra -->  Allow to edit the broker info in the webform on Mar 16, 2018.
 *
 * Added the Testing Purpose PC Dave = 820, Awata = 2, Lendterra = 3126 PC's
 * Ticket ID : 156022288
 **/
$allowUserToEditBroker = 0;
if (glAllowHMLOPCToEditBrokerINLV::is($PCID) && $emailOpt == 'Email' && $LMRId > 0 && $allowToEdit) {
    $allowUserToEditBroker = 1;
}

if ($publicUser == 1 && ($webFormFOpt != 'agent' || $loanOfficerId > 0)) { // Agent Section Only shows Branch Web Form Only.
    $preferredLoanOfficerBrokerInfoArray = $preferredBrokerInfoArray = $preferredLoanofficerInfoArray = [];
    $agentSectionDiv = 'display: block;';

    if ($loanOfficerId > 0) {
        //echo $loanOfficerId;
        $brokerList = [];
        $ip['externalBroker'] = 0;
        $ip['loanOfficerId'] = $loanOfficerId;
        $ip['userGroup'] = 'Agent';
        $ip['_publicUser'] = $publicUser;
        $ip['PCID'] = $PCID;

        if ($loanOfficerId > 0) {
            $brokerList = listAllAgentsLoanOfficer::getReport($ip);
        }
        foreach ($brokerList as $brokeachKey => $eachBrokerList) {
            $brokerListNewArray[$brokeachKey] = $eachBrokerList;
            $brokerListNewArray[$brokeachKey]['brokerFName'] = $eachBrokerList['bName'];
            $brokerListNewArray[$brokeachKey]['brokerLName'] = $eachBrokerList['bLName'];
        }
        $preferredLoanOfficerBrokerInfoArray = $brokerListNewArray;
    } else {
        $inArray = [
            'executiveId' => $executiveId,
            'fOpt'        => $webFormFOpt,
            '_publicUser' => $publicUser
        ];
        $preferredLoanOfficerBrokerInfoArray = getBranchBrokerList::getReport($inArray);
    }

    foreach ($preferredLoanOfficerBrokerInfoArray as $eachLBuser) {
        if ($eachLBuser['externalBroker'] == '0') {
            $preferredBrokerInfoArray[] = $eachLBuser;
        }
        if ($eachLBuser['externalBroker'] == '1') {
            $preferredLoanofficerInfoArray[] = $eachLBuser;
        }
    }

    /*
     * “Agent/Broker Info” as a label “Loan Officer/Mortgage Broker Info” - Dayton Capital Partners, LLC
     * Ticket ID : 156333030
     * Mar 28, 2018
     * Make it as global on Mar 30, 2018
    */
    $agentLabel = 'Agent/Broker';
    if (in_array($PCID, $glAgentLabelChanges)) {
        $agentLabel = 'Loan Officer/Broker';
    }
//$PCquickAppFieldsInfo = array();
    if (($emailOpt != 'Email' || $allowUserToEditBroker == 1)) {
        $secArr = BaseHTML::sectionAccess2(['sId' => 'ABI', 'opt' => $fileTab]);
        loanForm::pushSectionID('ABI');
        ?>

        <div class="card card-custom ABI ABICard <?php if (count(Arrays::getValueFromArray('ABI', $fieldsInfo)) <= 0) {
            echo 'secHide';
        } ?>">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">
                        <?php echo BaseHTML::getSectionHeading('ABI'); ?>
                    </h3>
                    <?php if (trim(BaseHTML::getSectionTooltip('ABI')) != '') { ?>&nbsp;
                        <i class="popoverClass fas fa-info-circle text-primary "
                           data-html="true"
                           data-content="<?php echo BaseHTML::getSectionTooltip('ABI'); ?>"></i>
                    <?php } ?>
                </div>
                <div class="card-toolbar ">
                    <a href="javascript:void(0);"
                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                       data-card-tool="toggle"
                       data-section="ABICard"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-up icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </a>
                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                        <i class="ki ki-close icon-nm"></i>
                    </a>
                </div>
            </div>


            <div class="card-body ABICard_body">
                <!--  Broker -->
                <div id="REBrokerQuestion"
                     class="row form-group bg-gray-100 py-2  REBroker_disp <?php echo loanForm::showField('REBroker'); ?><?php if ($PCID == 2853) {
                         //echo 'secHide';
                     } //2853 is express, 3363 stage lw demo 2, 3580 is lendingwise-dave in live ?>"
                >
                    <?php echo loanForm::label('REBroker', 'col-md-6 '); ?>
                    <div class="col-md-6">
                        <?php
                        if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                            <input type="radio" class="radiobtn mandatory" name="REBroker" id="REBroker" value="Yes"
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $REBroker) . ' ' . $REBrokerYesBtn; ?>
                                   onclick="showAndHideBrokerInfo(this.value, 'BrokerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>Yes
                            <input type="radio" name="REBroker" id="REBroker" value="No"
                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $REBroker); ?>
                                   onclick="showAndHideBrokerInfo(this.value, 'BrokerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>No

                            <?php
                        } else {
                            echo '<b>' . $REBroker . '</b>';
                        }
                        ?>
                    </div>
                </div>
                <div id="BrokerInfoDiv"
                     class="  brokerSection  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'REBroker', 'sArr' => $secArr, 'pv' => $REBroker, 'av' => 'Yes']); ?>">
                    <!-- Broker Info Section Start -->
                    <div class="row">
                        <?php if (count($preferredBrokerInfoArray) > 0) { ?>
                            <div class=" col-md-6 LMRBroker_disp <?php echo loanForm::showField('LMRBroker'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('LMRBroker', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php
                                        if (($LMRId == 0 || $allowUserToEditBroker == 1)) {
                                            ?>
                                            <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRBroker', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    name="LMRBroker" id="LMRBroker"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    onchange="updateBrokerNo(this.value, 'loanModForm');populateBrokerInfo('loanModForm', this.value, 'DD');" <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRBroker', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <option value=""> - Select / New -</option>
                                                <?php
                                                for ($pd = 0; $pd < count($preferredBrokerInfoArray); $pd++) {
                                                    $tempPrefArray = [];
                                                    $selLMRBrokerNo = 0;
                                                    $selBrokerFName = '';
                                                    $selBrokerLName = '';
                                                    $selLMRBrokerName = '';
                                                    $sOpt = '';
                                                    $tempPrefArray = $preferredBrokerInfoArray[$pd];
                                                    $selLMRBrokerNo = cypher::myEncryption($tempPrefArray['brokerNumber']);
                                                    $selBrokerFName = $tempPrefArray['brokerFName'];
                                                    $selBrokerLName = $tempPrefArray['brokerLName'];
                                                    $selLMRBrokerName = $selBrokerFName . ' ' . $selBrokerLName;
                                                    if ($selLMRBrokerNo == cypher::myEncryption($agentNumber)) $sOpt = 'selected';
                                                    ?>
                                                    <option value="<?php echo $selLMRBrokerNo; ?>" <?php echo $sOpt ?> ><?php echo $selLMRBrokerName ?></option>
                                                <?php } ?>
                                            </select>
                                        <?php } else {
                                            echo '<b>' . $brokerFName . ' ' . $brokerLName . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        <?php }
                        ?>
                        <div class=" col-md-6 <?php echo loanForm::showField('REBrokerEmail'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('REBrokerEmail', 'col-md-5 '); ?>
                                <div class="col-md-7">
                                    <?php
                                    if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                        ?>
                                        <input type="email"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="REBrokerEmail" id="REBrokerEmail"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo $brokerEmail; ?>" autocomplete="off"
                                               onblur="checkREBrokerEmailExist('loanModForm');" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                    <?php } else {
                                        echo '<b>' . $brokerEmail . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="<?php echo $LmbInfo; ?>" class="LmbInfo row ">

                        <div class=" col-md-6 REBrokerFirstName_disp <?php echo loanForm::showField('REBrokerFirstName'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('REBrokerFirstName', 'col-md-5 '); ?>
                                <div class="col-md-7">
                                    <?php
                                    if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                        ?>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerFirstName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="REBrokerFirstName" id="REBrokerFirstName"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo $brokerFName; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerFirstName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php
                                    } else {
                                        echo '<b>' . $brokerFName . '</b>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 REBrokerLastName_disp <?php echo loanForm::showField('REBrokerLastName'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('REBrokerLastName', 'col-md-5 '); ?>
                                <div class="col-md-7">
                                    <?php
                                    if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                        ?>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerLastName', 'sArr' => $secArr, 'opt' => 'M']); ?>>"
                                               name="REBrokerLastName" id="REBrokerLastName"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo $brokerLName; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerLastName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php
                                    } else {
                                        echo '<b>' . $brokerLName . '</b>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6 REBrokerCompany_disp <?php echo loanForm::showField('REBrokerCompany'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('REBrokerCompany', 'col-md-5 '); ?>
                                <div class="col-md-7">
                                    <?php
                                    if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                        ?>
                                        <input type="text"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerCompany', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="REBrokerCompany" id="REBrokerCompany"
                                               tabindex="<?php echo $tabIndex++; ?>" class="form-control input-sm"
                                               value="<?php echo $brokerCompany; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'REBrokerCompany', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else {
                                        echo '<b>' . $brokerCompany . '</b>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 brokerPhone_disp <?php echo loanForm::showField('brokerPhone'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('brokerPhone', 'col-md-5 '); ?>
                                <div class="col-md-7 brokerSection">
                                    <?php
                                    if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                        ?>
                                        <input type="text"
                                               class="form-control input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="brokerPhone" id="brokerPhone"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo $brokerPhone; ?>" autocomplete="off"
                                               placeholder="(___) ___ - ____ Ext ____" <?php echo BaseHTML::fieldAccess(['fNm' => 'brokerPhone', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php
                                    } else {
                                        echo '<b>' . Strings::formatPhoneNumber($brokerPhone) . '</b>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <?php //} ?>
                <!--  End of Broker-->

                <?php if ($loanOfficerId == '0') { ?>
                    <!--  Loan Officer -->
                    <div id="RELoanofficerQuestion"
                         class="row form-group  bg-gray-100 py-2  RELoanofficer_disp <?php echo loanForm::showField('RELoanofficer'); ?><?php if ($PCID == 2853) {
                             echo 'secHide';
                         } //2853 is express, 3363 stage lw demo 2, 3580 is lendingwise-dave in live ?>"
                    >
                        <?php echo loanForm::label('RELoanofficer', 'col-md-5 '); ?>
                        <div class="col-md-6">
                            <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                <input type="radio" class="radiobtn mandatory" name="RELoanofficer"
                                       id="RELoanofficer"
                                       value="Yes"
                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $RELoanofficer) . ' ' . $RELoanofficerYesBtn; ?>
                                       onclick="showAndHideLoanofficerInfo(this.value, 'LoanofficerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>Yes
                                <input type="radio" name="RELoanofficer" id="RELoanofficer" value="No"
                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $RELoanofficer); ?>
                                       onclick="showAndHideLoanofficerInfo(this.value, 'LoanofficerInfoDiv');" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>No
                                <?php
                            } else {
                                if ($RELoanofficer == '') {
                                    $RELoanofficer = 'No';
                                }
                                echo '<b>' . $RELoanofficer . '</b>';
                            }
                            ?>
                        </div>
                    </div>

                    <div id="LoanofficerInfoDiv"
                         class="loanofficerSection  <?php echo BaseHTML::parentFieldAccess(['fNm' => 'RELoanofficer', 'sArr' => $secArr, 'pv' => $RELoanofficer, 'av' => 'Yes']); ?>">
                        <!-- Loan Officer Section Start -->

                        <div class="row">
                            <?php
                            if (count($preferredLoanofficerInfoArray) > 0) { ?>
                                <div class=" col-md-6 LMRLoanofficer_disp <?php echo loanForm::showField('LMRLoanofficer'); ?>">
                                    <div class="row form-group">
                                        <?php echo loanForm::label('LMRLoanofficer', 'col-md-5 '); ?>
                                        <div class="col-md-7">
                                            <?php if (($LMRId == 0 || $allowUserToEditBroker == 1)) { ?>
                                                <select class="form-control input-sm disableLoanofficerfields <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRLoanofficer', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        name="LMRLoanofficer" id="LMRLoanofficer"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        onchange="updateLoanofficerNo(this.value, 'loanModForm');populateLoanofficerInfo('loanModForm', this.value, 'DD');" <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRLoanofficer', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                    <option value=""> - Select / New -</option>
                                                    <?php
                                                    for ($pd = 0; $pd < count($preferredLoanofficerInfoArray); $pd++) {
                                                        $tempPrefArray = [];
                                                        $selLMRLoanofficerNo = 0;
                                                        $selBrokerFName = '';
                                                        $selBrokerLName = '';
                                                        $selLMRLoanofficerName = '';
                                                        $sOpt = '';
                                                        $tempPrefArray = $preferredLoanofficerInfoArray[$pd];
                                                        $selLMRLoanofficerNo = cypher::myEncryption($tempPrefArray['brokerNumber']);
                                                        $selBrokerFName = $tempPrefArray['brokerFName'];
                                                        $selBrokerLName = $tempPrefArray['brokerLName'];
                                                        $selLMRLoanofficerName = $selBrokerFName . ' ' . $selBrokerLName;
                                                        if ($selLMRLoanofficerNo == cypher::myEncryption($agentNumber)) $sOpt = 'selected';
                                                        ?>
                                                        <option value="<?php echo $selLMRLoanofficerNo; ?>" <?php echo $sOpt ?> ><?php echo $selLMRLoanofficerName ?></option>
                                                    <?php } ?>
                                                </select>
                                            <?php } else {
                                                echo '<b>' . $loanofficerFName . ' ' . $loanofficerLName . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                            <?php }
                            ?>
                            <div class=" col-md-6 <?php echo loanForm::showField('RELoanofficerEmail'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('RELoanofficerEmail', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                            <input type="email"
                                                   class="form-control input-sm disableLoanofficerfields <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="RELoanofficerEmail" id="RELoanofficerEmail"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $loanofficerEmail; ?>" autocomplete="off"
                                                   onblur="checkLoanofficerEmailExist('loanModForm');" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerEmail', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                        <?php } else {
                                            echo '<b>' . $loanofficerEmail . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="<?php echo $LmbInfo; ?>" class="LmbInfo row ">
                            <div class="col-md-6 RELoanofficerFirstName_disp <?php echo loanForm::showField('RELoanofficerFirstName'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('RELoanofficerFirstName', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                            <input type="text"
                                                   class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerFirstName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="RELoanofficerFirstName" id="RELoanofficerFirstName"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $loanofficerFName; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerFirstName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . $loanofficerFName . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>

                            <div class=" col-md-6 RELoanofficerLastName_disp <?php echo loanForm::showField('RELoanofficerLastName'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('RELoanofficerLastName', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                            <input type="text"
                                                   class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerLastName', 'sArr' => $secArr, 'opt' => 'M']); ?>>"
                                                   name="RELoanofficerLastName" id="RELoanofficerLastName"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $loanofficerLName; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerLastName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . $brokerLName . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <div class=" col-md-6 RELoanofficerCompany_disp <?php echo loanForm::showField('RELoanofficerCompany'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('RELoanofficerCompany', 'col-md-5 '); ?>
                                    <div class="col-md-7">
                                        <?php
                                        if ($LMRId == 0 || $allowUserToEditBroker == 1) {
                                            ?>
                                            <input type="text"
                                                   class="form-control disableLoanofficerfields input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerCompany', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="RELoanofficerCompany" id="RELoanofficerCompany"
                                                   tabindex="<?php echo $tabIndex++; ?>" class="form-control input-sm"
                                                   value="<?php echo $loanofficerCompany; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'RELoanofficerCompany', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else {
                                            echo '<b>' . $loanofficerCompany . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 LoanofficerPhone_disp <?php echo loanForm::showField('LoanofficerPhone'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label('LoanofficerPhone', 'col-md-5 '); ?>
                                    <div class="col-md-7 loanofficerSection">
                                        <?php if ($LMRId == 0 || $allowUserToEditBroker == 1) { ?>
                                            <input type="text"
                                                   class="form-control disableLoanofficerfields input-sm mask_phone <?php echo BaseHTML::fieldAccess(['fNm' => 'LoanofficerPhone', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="LoanofficerPhone" id="LoanofficerPhone"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo $LoanofficerPhone; ?>" autocomplete="off"
                                                   placeholder="(___) ___ - ____ Ext ____"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'LoanofficerPhone', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php
                                        } else {
                                            echo '<b>' . Strings::formatPhoneNumber($LoanofficerPhone) . '</b>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php //} ?>
                    <!-- end of Loan Officer -->
                <?php } ?>
            </div>
        </div>
        <?php

    }
}  // Agent Section Only shows Branch Web Form Only.
?>
<?php
if ($publicUser == 1) {
    if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'Admin', 'opt' => $fileTab])) > 0) {
        loanForm::pushSectionID('Admin');

        ?>
        <div style="<?php echo $LMRClientTypeDisplay; ?>" class="card card-custom lmrClientTypeDisp">
            <div class="card-body">
                <div class=" row">
                    <div class="col-md-6">
                        <div class="form-group row ">
                            <label class="col-md-5 font-weight-bold"
                                   for="LMRClientType"><?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                <i id="loanprogramtooltip"
                                   class="fa fa-info-circle text-primary tooltipClass"
                                   title=""></i>
                            </label>
                            <div class="col-md-7">
                                <select class="form-control mandatory input-sm" data-placeholder=""
                                        name="LMRClientType[]" onchange="load1003Fields()"
                                        id="LMRClientType" tabindex="<?php echo $tabIndex++; ?>">
                                    <option value="">- Select -</option>
                                    <?php
                                    $serviceCnt = 0;
                                    if (count($servicesRequested) > 0) $serviceCnt = count($servicesRequested);
                                    for ($j = 0; $j < $serviceCnt; $j++) {
                                        $LMRClientTypeCode = '';
                                        $sOpt = '';
                                        $LMRClientType = '';
                                        $chk = '';
                                        $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                        $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                        $chk = Strings::isKeyChecked($LMRClientTypeInfo, 'ClientType', $LMRClientTypeCode);
                                        if (trim($chk) == 'checked') $chk = 'selected ';
                                        $displayOption = '';
                                        if ($LMRClientTypeCode == 'TBD' && $LMRClientTypeInfo[0]['ClientType'] != 'TBD') {
                                            $displayOption = "style = 'display:none;' ";
                                        }
                                        if ($servicesRequested[$j]['internalLoanProgram'] == 0) { ?>
                                            <option <?php echo $chk; ?> <?php echo $displayOption; ?>
                                                    value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                        <?php }
                                    }
                                    if (in_array('TBD', $fileLP) && $LMRId > 0) { ?>
                                        <!--                                        <option selected-->
                                        <!--                                                value="--><?php //echo 'TBD'; ?><!--">--><?php //echo 'TBD'; ?><!--</option>-->
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!--    sbaLoanProduct_disp <?php /*echo BaseHTML::fieldAccess(array('fNm' => 'sbaLoanProduct', 'sArr' => $secArr, 'opt' => 'D')); */ ?>"

                -->
                <div class=" row sbaLoanProduct_disp <?php echo loanForm::showField('sbaLoanProduct'); ?>">
                    <div class="col-md-6">
                        <div class="form-group row ">
                            <?php echo loanForm::label('sbaLoanProduct', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <select data-placeholder="Select SBA Loan Product" name="sbaLoanProduct"
                                        id="sbaLoanProduct"
                                        tabindex="<?php echo $tabIndex++; ?>"
                                        class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'sbaLoanProduct', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        data-placeholder="Select SBA Loan Product">
                                    <option></option>
                                    <?php
                                    if (count($HMLOPCBasicSBALoanProductInfoArray ?? []) > 0) {
                                        foreach ($HMLOPCBasicSBALoanProductInfoArray as $eachSBALoanProductID) { ?>
                                            <option value="<?php echo $eachSBALoanProductID; ?>" <?php if (Strings::showField('sbaLoanProduct', 'ResponseInfo') == $eachSBALoanProductID) {
                                                echo 'selected';
                                            } ?>><?php echo $globalSBALoanProductsCat[$eachSBALoanProductID]; ?></option>
                                        <?php }
                                    } else {
                                        foreach ($globalSBALoanProductsCat as $eachSBALoanProductID => $eachSBALoanProductVal) { ?>
                                            <option value="<?php echo $eachSBALoanProductID; ?>" <?php if (Strings::showField('sbaLoanProduct', 'ResponseInfo') == $eachSBALoanProductID) {
                                                echo 'selected';
                                            } ?>><?php echo $eachSBALoanProductVal; ?></option>
                                        <?php }
                                    } ?>

                                </select>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="loanInfoLPSection row <?php echo loanForm::showField('LMRClientType'); ?> "
                     style="<?php echo $loanInfoLPSectionDisp ?>">
                    <div class=" col-md-6 additionalLoanProgram_disp <?php echo loanForm::showField('additionalLoanProgram'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('additionalLoanProgram', 'col-md-5', '', ':'); ?>
                            <div class="col-md-7">
                                <select class=" chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'additionalLoanProgram', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        data-placeholder="Select Additional Loan Programs"
                                        id="LMRadditionalLoanProgram"
                                        name="LMRadditionalLoanProgram[]" multiple="">
                                    <?php
                                    $serviceCnt = 0;
                                    if (count($servicesRequested) > 0) $serviceCnt = count($servicesRequested);
                                    for ($j = 0; $j < $serviceCnt; $j++) {
                                        $LMRClientTypeCode = '';
                                        $sOpt = '';
                                        $LMRClientType = '';
                                        $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                        $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                        $chk = '';
                                        if (in_array($LMRClientTypeCode, $myFileInfo['LMRadditionalLoanprograms'] ?? [])) {
                                            $chk = 'selected ';
                                        }
                                        $displayOption = '';
                                        if ($LMRClientTypeCode == 'TBD') {
                                            $displayOption = "style = 'display:none;' ";
                                        }
                                        if ($servicesRequested[$j]['internalLoanProgram'] == 0) { ?>
                                            <option <?php echo $chk; ?> <?php echo $displayOption; ?>
                                                    value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                            <?php
                                        }
                                    } ?>
                                </select></div>
                        </div>
                    </div>

                    <div class="col-md-6 leadSource_disp <?php echo loanForm::showField('leadSource'); ?>">
                        <div class="row form-group">
                            <?php echo loanForm::label('leadSource', 'col-md-5 '); ?>
                            <div class="col-md-7">
                                <select class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                        name="branchLeadSource" id="leadSource" TABINDEX="<?php echo $tabIndex++ ?>"
                                        onchange="checkRef(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <option value="">- Select -</option>
                                    <?php
                                    for ($bh = 0; $bh < count($branchHAInfoArray); $bh++) {
                                        $branchHearAbout = '';
                                        $sOpt = '';
                                        $branchHearAbout = $branchHAInfoArray[$bh]['branchHearAbout'];
                                        $sOpt = Arrays::isSelected($branchHearAbout, $leadSource);
                                        ?>
                                        <option <?php echo $sOpt; ?>
                                                value="<?php echo $branchHearAbout; ?>"><?php echo $branchHearAbout; ?></option>
                                        <?php
                                    }
                                    ?>
                                    <option <?php echo Arrays::isSelected('Other', $leadSource) ?> value="Other">Other
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class=" col-md-6 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'pv' => $leadSource, 'av' => 'Other']); ?>"
                         id="refDiv">
                <textarea class="form-control input-sm " name="hereAbout" id="hereAbout"
                          tabindex="<?php echo $tabIndex++; ?>"
                          placeholder="Let us know how you heard about us."><?php echo $hereAbout; ?></textarea>
                    </div>


                    <div class="col-md-6 referringParty_disp <?php echo loanForm::showField('referringParty'); ?>">
                        <div class="row form-group">
                            <label class="col-md-5 font-weight-bold"
                                   for="referringParty"><?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                            <div class="col-md-7">
                                <?php if ($allowToEdit) { ?>
                                    <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                           type="text" name="referringParty" id="referringParty"
                                           value="<?php echo $referringParty; ?>" autocomplete="off"
                                           TABINDEX="<?php echo $tabIndex++ ?>"
                                           maxlength="45" <?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                <?php } else { ?>
                                    <h5><?php echo $referringParty; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
} // Public User..
?>

<script>
    function load1003Fields() {
        $('.BCI ,.AG,.BEI,.BMI,.SORE,.FAS,.HMLOLoanInfoSections,.LIE,.GOG,.LT,.PD,.ONM,.AQ,.agreeTCCard').show();
        $('.BCI ,.AG,.BEI,.BMI,.SORE,.FAS,.HMLOLoanInfoSections,.LIE,.GOG,.LT,.PD,.ONM,.AQ,.agreeTCCard').removeClass('secHide');
        $('.LT').removeClass('d-none');
        $('#saveBtnDiv').show();
        $('#saveBtnDiv').removeClass('d-none');
        signatureForm._signCard.show();

    }

</script>

<!-- webForm1003.php -->
