<?php

use models\lendingwise\db\tblFileUpdatedDate_db;
use models\lendingwise\tblFileUpdatedDate;
use models\standard\Dates;

require __DIR__ . '/../../../../public/includes/util.php';

$LMRId = 6313187;
$cur_date = '2025-08-22';

$lastUpdate = tblFileUpdatedDate::Get([
    tblFileUpdatedDate_db::COLUMN_FILEID => $LMRId,
]) ?? new tblFileUpdatedDate();

$lastUpdate->fileID = $LMRId;
$lastUpdate->lastUpdatedDate = Dates::Timestamp($cur_date);
pr($lastUpdate->lastUpdatedDate);

$lastUpdate->Save();
