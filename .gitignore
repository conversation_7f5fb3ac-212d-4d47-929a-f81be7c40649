## generic files to ignore
## ######################
*~
!.git/hooks/post-merge
*.DS_Store
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
# removed icon from here in next line bc it was stopping tinymce icon folder from showing
#Icon?
*.swp
*.out
.eslintrc.js
dump.rdb
ehthumbs.db
Thumbs.db
thumbs.db
config.php
.idea
/idea/*
report.*
.vscode
## Logs and databases #
# ######################
*.log
*.sqlite
*.sqlite3

test.php
dave/
cgi-bin/
cgi-bin/*
specs/
doc/
leadDoc/
public/PCLogo/
PCUpDoc/
lenderDoc/
trustDocs/
LMRFileDoc/
LMRFileDoc_new/
LMRFileDoc_S3/
domain_search/
domainwhois/
.env
.env.ORIGINAL_BACKUP

trainingVideos/
TS/attachments/
public/.htaccess
public/clientListXls
public/assets/scripts/cache/
public/assets/css/cache/
public/assets/js/cache/
public/services/*
public/temp/*
public/temp
clientListXls/*
clientListXls
keys/
public/branchLogo/
lenderXls/
google_scripts/
lenderXls/*/*.*
vendor/*
!vendor/.gitkeep
!vendor/tecnickcom
vendor/tecnickcom/*
!vendor/tecnickcom/tcpdf/
vendor/tecnickcom/tcpdf/*
!vendor/tecnickcom/tcpdf/fonts
error_log
public/bootstrapeditor/uploads
bootstrapeditor/uploads/*
public/assets/css/cache/*
public/assets/js/cache/*
public/lib/tcpdf/cache/*
public/tmp/*
public/tmp/
TLPUpload/
models/lendingwise/db/
models/lendingwise/tblFileHistory.php
#
nbproject/*
pops/popsConfig.php

#Added on 06-Mar-2019
#images/*
public/lib/tcpdf/cache/*
img/*

#ended 06-Mar-2019
git rm --cached pops/popsConfig.php

includes/config.php
git rm --cached includes/config.php

#robots.txt
#git rm --cached robots.txt

public/assets/scripts/config.js
git rm --cached public/assets/js/config.js

git rm --cached lenderXls
git rm --cached public/PCLogo
git rm --cached public/branchLogo

git rm --cached temp

git rm --cached temp/*.*

git rm --cached temp/*/*.*

git rm --cached design/*/*.*
git rm --cached design/*.*

git rm --cached dave/*.*
git rm --cached dave/*/*.*

git rm --cached tcpdf/cache/*.*
git rm --cached bootstrapeditor/uploads/*.*
git rm --cached /.htaccess
/.htaccess

git rm --cached auction/admin/error_log
auction/admin/error_log

git rm --cached config.php
/composer.lock
/scripts/pdf
/scripts/pops
/.env.dev
/logs
/models/lendingwise/db
/tests/models/composite/assert
/database/_errors/*

/_test_files/error_logs
/output/*.html
/tests/models/inArrayData_output/*.json
/scripts/output
/.dccache
/snyk.txt
/.env.prod
public/PCLogo
public/branchLogo
public/trustDocs

/tasks_dev/fileUsage
/tests/models/inArrayData_output
public/brokerLogo
/database/2023/09/27/collation/__dev.bat

/models/lendingwise_api/db
/models/lendingwise_chargebee/db
/models/lendingwise_fci/db
/models/lendingwise_log/db
/models/lendingwise_datawarehouse/db
/tests/siteCheck
/tests/pops/exportClientFiles/getCSVArray
/public/assets/css/compiled
/public/assets/js/compiled
/models/lendingwise_datawarehouse/db
/tests/models/composite/oEmployee/getEmployeeList
/tests/models/composite/oAutomatedRules/values_set.json

db_data/*
node_modules
cypress/screenshots/*
cypress/videos/*
cypress/downloads/*
runner-results
.phpunit.result.cache
userFiles/*
public/version.json
.env:Zone.Identifier
cypress/downloads/

