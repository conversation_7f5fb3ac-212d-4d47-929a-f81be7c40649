name: Production-Deploy

run-name: "Production Deploy"

on:
  workflow_dispatch:
    inputs:
      action:
        type: choice
        description: "Choose action"
        required: false
        options:
          - deploy
          - scaleback
      image_id:
        type: string
        required: false
        description: "ECR Image ID (default: copies Staging)"

env:
  AWS_REGION: us-east-2
  ECR_REPO: lendingwise-legacy
  ECS_CLUSTER: lendingwise-legacy
  ECS_SERVICE: lendingwise-service
  CLICKUP_WORKSPACE_ID: 9011531852
  CLICKUP_CHANNEL_ID: 8cj242c-13071 # channel = "Notification Sandbox-A"

jobs:
  deploy:
    if: ${{ !github.event.inputs.action || github.event.inputs.action == 'deploy' }}
    name: Deploy Container
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Configure AWS credentials (assume Staging)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: arn:aws:iam::************:role/OrganizationAccountAccessRole
          role-session-name: StagingEnv

      - name: Copy Staging ECS image ID
        run: |
          # use input IMAGE_ID if provided else default to current Staging env Image ID
          INPUT_IMAGE_ID="${{ inputs.image_id }}"
          if [ -n "${INPUT_IMAGE_ID}" ]; then
            echo "📦 Using provided Image ID from input: ${INPUT_IMAGE_ID}" | tee -a $GITHUB_STEP_SUMMARY
            echo "USE_CONTAINER_ID=${INPUT_IMAGE_ID}" >> $GITHUB_ENV
          else
            echo "📦 Fetching current image ID from Staging env ..."
            CURRENT_TASK_ID=$(aws ecs list-tasks --cluster ${{ env.ECS_CLUSTER }} --desired-status RUNNING --query "taskArns[0]" --output text | awk -F/ '{print $NF}')
            STAGING_CONTAINER_ID=$(aws ecs describe-tasks --cluster ${{ env.ECS_CLUSTER }} --tasks $CURRENT_TASK_ID --query "tasks[0].containers[0].image" --output text)
            echo "Staging Container ID: $STAGING_CONTAINER_ID" | tee -a $GITHUB_STEP_SUMMARY
            echo "USE_CONTAINER_ID=$STAGING_CONTAINER_ID" >> $GITHUB_ENV
          fi

      - name: Configure AWS credentials (assume Prod)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: arn:aws:iam::************:role/OrganizationAccountAccessRole
          role-session-name: ProdEnv

      - name: Deploying Image ID to ECS
        run: |
          echo "🔍 Fetching current task definition ..."
          aws ecs describe-task-definition --task-definition lendingwise-legacy --query taskDefinition --output json > task-def.json
          
          echo "🛠️ Updating image using jq ..."
          jq '.containerDefinitions[0].image = "'"$USE_CONTAINER_ID"'" | del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)' task-def.json > task-def-updated.json

          echo "📦 Registering new task definition..."
          NEW_TASK_DEF_ARN=$(aws ecs register-task-definition --cli-input-json file://task-def-updated.json | jq -r '.taskDefinition.taskDefinitionArn')

          echo "🚀 Updating ECS service with new task definition..."
          aws ecs update-service --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.ECS_SERVICE }} --task-definition "$NEW_TASK_DEF_ARN" --force-new-deployment

          echo "🚀 Deployed: $ECR_REPO" >> $GITHUB_STEP_SUMMARY

      # Ensure DB isn't left undersized after a prior scaleback
      - name: Ensure RDS >= db.t3.medium
        run: |
          set -euo pipefail
          CURRENT_CLASS=$(aws rds describe-db-instances \
            --db-instance-identifier lendingwise-prod \
            --query 'DBInstances[0].DBInstanceClass' --output text)
          echo "Current DB class: ${CURRENT_CLASS}" | tee -a $GITHUB_STEP_SUMMARY

          case "${CURRENT_CLASS}" in
            db.t3.nano|db.t3.micro|db.t3.small)
              echo "Upsizing ${CURRENT_CLASS} -> db.t3.medium" | tee -a $GITHUB_STEP_SUMMARY
              aws rds modify-db-instance \
                --db-instance-identifier lendingwise-prod \
                --db-instance-class db.t3.medium \
                --apply-immediately
              echo "Waiting for DB to become available..."
              aws rds wait db-instance-available --db-instance-identifier lendingwise-prod
              ;;
            *)
              echo "DB size is ${CURRENT_CLASS} (>= medium) — no change." | tee -a $GITHUB_STEP_SUMMARY
              ;;
          esac

      # Ensure desired count ≥ 1 in case service was previously scaled to 0
      - name: Ensure desired count ≥ 1
        run: |
          aws ecs update-service \
            --cluster "${{ env.ECS_CLUSTER }}" \
            --service "${{ env.ECS_SERVICE }}" \
            --desired-count 1

      - name: Wait until ECS container is Ready
        run: |
          aws ecs list-tasks --cluster ${{ env.ECS_CLUSTER }}

          NEW_TASK_ID=$(aws ecs list-tasks --cluster ${{ env.ECS_CLUSTER }} --query "taskArns[0]" --output text | awk -F/ '{print $NF}')

          # if the new task id is different than the previous active task id
          # then lets wait for the new task to finish launching.
          if [ "$CURRENT_TASK_ID" != "$NEW_TASK_ID" ]; then
            aws ecs wait tasks-running --cluster ${{ env.ECS_CLUSTER }} --task $NEW_TASK_ID
          fi

          echo "✅ App is Ready" >> $GITHUB_STEP_SUMMARY

      - name: Get workflow status
        uses: martialonline/workflow-status@v3
        id: check_workflow_status

      - name: Workflow status update on ClickUp chat
        uses: smplrspace/clickup-chat-action@v1
        with:
          workspace-id: ${{ env.CLICKUP_WORKSPACE_ID }}
          channel-id: ${{ env.CLICKUP_CHANNEL_ID }}
          status-update: true
          status: ${{ steps.check_workflow_status.outputs.status }}
        env:
          CLICKUP_TOKEN: ${{ secrets.CLICKUP_TOKEN }}

  scaleback:
    if: ${{ !github.event.inputs.action || github.event.inputs.action == 'scaleback' }}
    name: Scale Back PROD ECS (and DB)
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: arn:aws:iam::************:role/OrganizationAccountAccessRole
          role-session-name: ProdEnv

      - name: Scale ECS service to 0
        run: |
          echo "🔻 Scaling down PROD service $ECS_SERVICE to 0 tasks..." | tee -a $GITHUB_STEP_SUMMARY
          aws ecs update-service \
            --cluster "$ECS_CLUSTER" \
            --service "$ECS_SERVICE" \
            --desired-count 0
          echo "::notice title=Scaleback::Prod ECS service $ECS_SERVICE scaled to 0"

      - name: Resize RDS to db.t3.micro
        run: |
          set -euo pipefail
          CURRENT_CLASS=$(aws rds describe-db-instances \
            --db-instance-identifier lendingwise-prod \
            --query 'DBInstances[0].DBInstanceClass' --output text)
          echo "Current DB class: ${CURRENT_CLASS}" | tee -a $GITHUB_STEP_SUMMARY

          if [ "${CURRENT_CLASS}" != "db.t3.micro" ]; then
            echo "Resizing ${CURRENT_CLASS} -> db.t3.micro" | tee -a $GITHUB_STEP_SUMMARY
            aws rds modify-db-instance \
              --db-instance-identifier lendingwise-prod \
              --db-instance-class db.t3.micro \
              --apply-immediately
            echo "Waiting for DB to become available..."
            aws rds wait db-instance-available --db-instance-identifier lendingwise-prod
          else
            echo "DB already at db.t3.micro — no change." | tee -a $GITHUB_STEP_SUMMARY
          fi

      - name: Notify ClickUp (Scaleback)
        env:
          GITHUB_RUN_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        run: |
          echo "📢 Sending ClickUp scaleback notification..."
          curl -sS -X POST "https://api.clickup.com/api/v2/room/${CLICKUP_CHANNEL_ID}/message" \
            -H "Authorization: ${{ secrets.CLICKUP_API_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d "{
              \"content\": \"🔻 Production *${ECS_SERVICE}* scaled down on *${ECS_CLUSTER}* (run: ${GITHUB_RUN_URL}).\",
              \"workspace_id\": \"${CLICKUP_WORKSPACE_ID}\"
            }" | tee -a $GITHUB_STEP_SUMMARY
