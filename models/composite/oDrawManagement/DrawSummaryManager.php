<?php

namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\PageVariables;
use models\standard\Strings;
use models\standard\Dates;
use models\lendingwise\tblFileSimpleDrawRequests;
use models\lendingwise\tblDrawSummaryCalculatedValues;

class DrawSummaryManager extends strongType
{
    private ?array $drawRequestHistory = null;
    private bool $isSimpleMode = false;
    private ?int $LMRId = null;
    public static ?string $address = null;
    public static ?string $city = null;
    public static ?string $state = null;
    public static ?string $zip = null;
    public static float $initialLoan = 0;
    public static float $rehabCostFinanced = 0;
    public static float $rehabCostPercentageFinanced = 0;
    public static float $totalLoanAmount = 0;
    public static float $currentLoanBalance = 0;
    public static float $rehabCost = 0;
    public static ?string $arv = null;
    public static float $totalDrawsFunded = 0;
    public static float $holdbackRemaining = 0;
    public static ?string $closingDate = null;
    public static ?string $maturityDate = null;
    public static ?string $dateOfLastDraw = null;
    public static ?string $dateOfCurrentDraw = null;

    private ?tblDrawSummaryCalculatedValues $drawSummaryCalculatedValues = null;

    public function __construct(int $LMRId, ?DrawRequestManager $drawRequestManager = null)
    {
        $this->LMRId = $LMRId;
        if (!$drawRequestManager) {
            $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
        }
        $this->drawRequestManager = $drawRequestManager;
        $this->drawRequestHistory = $drawRequestManager->getDrawRequestHistory(true);
        $this->drawSummaryCalculatedValues = tblDrawSummaryCalculatedValues::Get(['LMRId' => $LMRId]);
        $templateManager = SowTemplateManager::forProcessingCompany(PageVariables::$PCID);
        $templateSettings = $templateManager->getTemplate();
        $this->isSimpleMode = (bool)$templateSettings->enableSimpleMode;
        $this->loadSummaryData();
    }

    /**
     * Static initializer method that takes constructor arguments
     * @param int $LMRId
     * @param DrawRequestManager|null $drawRequestManager
     * @return self
     */
    public static function initialize(int $LMRId, ?DrawRequestManager $drawRequestManager = null): self
    {
        return new self($LMRId, $drawRequestManager);
    }

    /**
     * Load all summary data and populate static properties
     * @return void
     */
    private function loadSummaryData(): void
    {
        self::$address = $this->safeShowField('propertyAddress', 'LMRInfo');
        self::$city = $this->safeShowField('propertyCity', 'LMRInfo');
        self::$state = $this->safeShowField('propertyState', 'LMRInfo');
        self::$zip = $this->safeShowField('propertyZip', 'LMRInfo');

        self::$initialLoan = $this->safeShowFieldFloat('InitialLoanAmount', 'fileCalculatedValues');
        self::$rehabCostFinanced = $this->drawSummaryCalculatedValues->rehabCostFinanced ?? 0;
        self::$totalLoanAmount = $this->drawSummaryCalculatedValues->totalLoanAmount ?? 0;
        self::$rehabCost = $this->drawSummaryCalculatedValues->rehabCost ?? 0;
        self::$totalDrawsFunded = $this->drawSummaryCalculatedValues->totalDrawsFunded ?? 0;
        self::$holdbackRemaining = $this->drawSummaryCalculatedValues->holdBackAmountRemaining ?? 0;
        self::$currentLoanBalance = $this->drawSummaryCalculatedValues->currentLoanBalance ?? 0;
        self::$rehabCostPercentageFinanced = $this->safeShowFieldFloat('rehabCostPercentageFinanced', 'fileHMLONewLoanInfo');
        self::$arv = $this->safeShowFieldFloat('assessedValue', 'listingRealtorInfo');

        self::$closingDate = $this->safeFormatDate($this->safeShowField('closingDate', 'QAInfo'));
        self::$maturityDate = $this->safeFormatDate($this->safeShowField('maturityDate', 'fileHMLOPropertyInfo'));

        $this->calculateDrawDates();
    }

    /**
     * Calculate draw-related data from history
     * @return void
     */
    private function calculateDrawDates(): void
    {
        self::$dateOfCurrentDraw = '-';
        self::$dateOfLastDraw = '-';

        if ($this->isSimpleMode) return;

        $lastDrawRequest = end($this->drawRequestHistory);
        $lastApprovedDrawRequest = null;

        foreach (array_reverse($this->drawRequestHistory) as $history) {
            if ($history['status'] === 'approved') {
                $lastApprovedDrawRequest = $history;
                break;
            }
        }

        if ($lastDrawRequest && $lastDrawRequest['status'] !== 'approved' && !empty($lastDrawRequest['submittedAt'])) {
            self::$dateOfCurrentDraw = $this->safeFormatDate($lastDrawRequest['submittedAt']);
        }

        if ($lastApprovedDrawRequest && !empty($lastApprovedDrawRequest['approvedAt'])) {
            self::$dateOfLastDraw = $this->safeFormatDate($lastApprovedDrawRequest['approvedAt']);
        }
    }

    /**
     * Safely get field value with fallback
     * @param string $fieldName
     * @param string $tableName
     * @return string
     */
    private function safeShowField(string $fieldName, string $tableName): string
    {
        try {
            $value = Strings::showField($fieldName, $tableName);
            return !empty(trim($value)) ? trim($value) : '';
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Safely get numeric field value with fallback to 0
     * @param string $fieldName
     * @param string $tableName
     * @return float
     */
    private function safeShowFieldFloat(string $fieldName, string $tableName): float
    {
        try {
            $value = Strings::showField($fieldName, $tableName);
            return !empty(trim($value)) ? (float)Strings::Numeric(trim($value)) : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Safely format date with fallback to '-'
     * @param string|null $date
     * @return string
     */
    private function safeFormatDate(?string $date): string
    {
        if (empty($date) || Dates::IsEmpty($date)) {
            return '-';
        }

        try {
            $formatted = Dates::formatDateWithRE($date, 'YMD', 'm/d/Y');
            return !empty($formatted) ? $formatted : '-';
        } catch (\Exception $e) {
            return '-';
        }
    }

    /**
     * Get formatted total draws funded for display
     * @return string
     */
    public static function getFormattedTotalDrawsFunded(): string
    {
        return number_format(self::$totalDrawsFunded);
    }

    /**
     * Get formatted holdback remaining for display
     * @return string
     */
    public static function getFormattedHoldbackRemaining(): string
    {
        return number_format(self::$holdbackRemaining);
    }

    /**
     * Get all summary data as an array
     * @return array
     */
    public static function getAllData(): array
    {
        return [
            'address' => self::$address,
            'city' => self::$city,
            'state' => self::$state,
            'zip' => self::$zip,
            'initialLoan' => self::$initialLoan,
            'rehabCostFinanced' => self::$rehabCostFinanced,
            'rehabCostPercentageFinanced' => self::$rehabCostPercentageFinanced,
            'totalLoanAmount' => self::$totalLoanAmount,
            'currentLoanBalance' => self::$currentLoanBalance,
            'rehabCost' => self::$rehabCost,
            'arv' => self::$arv,
            'totalDrawsFunded' => self::$totalDrawsFunded,
            'holdbackRemaining' => self::$holdbackRemaining,
            'closingDate' => self::$closingDate,
            'maturityDate' => self::$maturityDate,
            'dateOfLastDraw' => self::$dateOfLastDraw,
            'dateOfCurrentDraw' => self::$dateOfCurrentDraw,
        ];
    }
}
