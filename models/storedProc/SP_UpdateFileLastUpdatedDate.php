<?php

namespace models\storedProc;

use models\lendingwise\db\tblFileUpdatedDate_db;
use models\lendingwise\tblFileUpdatedDate;
use models\standard\Dates;
use models\types\strongType;

class SP_UpdateFileLastUpdatedDate extends strongType
{
    public static function getReport(
        int     $LMRId,
        ?string $cur_date,
        string  $opt
    )
    {
        // IN file_ID longtext, IN cur_date datetime, IN opt varchar(10)
        if (!$LMRId) {
            return;
        }

        $lastUpdate = tblFileUpdatedDate::Get([
            tblFileUpdatedDate_db::COLUMN_FILEID => $LMRId,
        ]) ?? new tblFileUpdatedDate();

        $lastUpdate->fileID = $LMRId;
        $lastUpdate->lastUpdatedDate = Dates::Timestamp();
        $lastUpdate->Save();

    }
}