<?php

namespace models\storedProc;

use models\lendingwise\db\tblFile_db;
use models\lendingwise\db\tblFileResponse_db;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileResponse;
use models\standard\Dates;
use models\types\strongType;

class SP_FileAdminSaveNew extends strongType
{
    public static function getReport(
        $LMRId,
        $statusId,
        $lastUpdatedDate,
        $fileNumber,
        $leadSource,
        $priorityLevel,
        $trialModReceivedDate,
        $firstModPaymentAmt,
        $borrowerCallBack,
        $lenderCallBack,
        $lenderSubmission,
        $closedDisposition,
        $resolutionType,
        $HAFADate,
        $welcomeCallDate,
        $bankCallCompleted,
        $appealDate,
        $attorneyReviewDate,
        $trialPaymentDate1,
        $trialPaymentDate2,
        $trialPaymentDate3,
        $retainerDate,
        $isSLMOnly,
        $receivedDate,
        $closedDate,
        $salesDate,
        $OSID,
        $processorNotes,
        $branchID,
        $agentID,
        $clientId,
        $userName,
        $employeeId,
        $UID,
        $userGroup,
        $executiveId,
        $isSysNotesPrivate,
        $escalationDate,
        $denialDate,
        $totalCallsPlaced,
        $isANINo,
        $ANINo,
        $PCID,
        $isScrName,
        $screenerName,
        $isHMLO,
        $loanNumber,
        $mortgageNotes,
        $closingDate,
        $lenderInternalNotes
    ): int
    {
        if (!$LMRId) {
            return 0;
        }

        $response = tblFileResponse::Get([
            tblFileResponse_db::COLUMN_LMRID => $LMRId,
        ]);

        if (!$response) {
            $response = new tblFileResponse();
            $response->LMRId = $LMRId;
        }

        $cur_date = Dates::Timestamp();

        $response->primeStatusId = $statusId;
        $response->lastUpdatedDate = $cur_date;
        $response->fileNumber = $fileNumber;
        $response->leadSource = $leadSource;
        $response->priorityLevel = $priorityLevel;

        if ($isSLMOnly == 0 && $isHMLO == 1) {
            $response->HAFADate = $HAFADate;
            $response->resolutionType = $resolutionType;
            $response->attorneyReviewDate = $attorneyReviewDate;
            $response->firstModPaymentAmt = $firstModPaymentAmt;
            $response->appealDate = $appealDate;
            $response->escalationDate = $escalationDate;

        } elseif ($isSLMOnly == 0 && $isHMLO == 0) {
            $response->HAFADate = $HAFADate;
            $response->resolutionType = $resolutionType;
            $response->attorneyReviewDate = $attorneyReviewDate;
            $response->trialPaymentDate1 = $trialPaymentDate1;
            $response->trialPaymentDate2 = $trialPaymentDate2;
            $response->trialPaymentDate3 = $trialPaymentDate3;
            $response->firstModPaymentAmt = $firstModPaymentAmt;
            $response->appealDate = $appealDate;
            $response->escalationDate = $escalationDate;
        }


        $response->trialModReceivedDate = $trialModReceivedDate;
        $response->borrowerCallBack = $borrowerCallBack;
        $response->lenderCallBack = $lenderCallBack;
        $response->lenderSubmissionDate = $lenderSubmission;
        $response->closedDisposition = $closedDisposition;
        $response->bankCallCompleted = $bankCallCompleted;
        $response->welcomeCallDate = $welcomeCallDate;
        $response->retainerDate = $retainerDate;
        $response->totalCallsPlaced = $totalCallsPlaced;
        $response->denialDate = $denialDate;
        $response->lenderInternalNotes = $lenderInternalNotes;

        if ($isANINo) {
            $response->ANINo = $ANINo;
        }

        if ($isScrName) {
            $response->screenerName = $screenerName;
        }

        $response->Save();

        $file = tblFile::Get([
            tblFile_db::COLUMN_LMRID => $LMRId,
        ]);

        $file->receivedDate = $receivedDate;
        $file->closedDate = $closedDate;
        $file->salesDate = $salesDate;
        if ($isHMLO) {
            $file->loanNumber = $loanNumber;
            $file->mortgageNotes = $mortgageNotes;
        }

        $file->Save();


        if ($statusId != $OSID && $statusId > 0 && $OSID > 0) {
            SP_SaveFileNotes::getReport(
                $processorNotes,
                $LMRId,
                $branchID,
                $agentID,
                $cur_date,
                '', // processorName
                $userName,
                $employeeId,
                'SH', // displayIn
                $isSysNotesPrivate,
                'GE', // notesType
                '', // sendTo
                $clientId,
                0, // LMRNotesId
                1, //isSysNotes
                $userGroup
            );

            SP_RecordFileStatus::getReport(
                $LMRId,
                $OSID,
                $statusId,
                'Pri',
                $UID,
                $userGroup,
                $executiveId,
                $cur_date,
                $cur_date
            );


        }

        SP_RecordFileTabUpdate::getReport(
            'U',
            'Admin Info',
            Dates::Timestamp(),
            (int)$PCID,
            $LMRId,
            $UID,
            $userGroup,
            1
        );

        return 1;

    }
}