<?php

namespace models\Controllers\backoffice;

use models\composite\oFile\getFileInfo\branchClientTypeInfo;
use models\composite\oFile\getFileInfo\fileModuleInfo;
use models\composite\oPC\getPCHMLOBasicLoanInfoForFileLevel;
use models\composite\oPC\getPCMinMaxLoanGuidelines;
use models\constants\accrualTypes;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPCID;
use models\constants\gl\glUserGroup;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\LMRequest\loanSetting;
use models\myFileInfo;
use models\PageVariables;
use models\paymentBased;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;
use models\types\strongType;

class LoanInfo extends strongType
{

    public static ?myFileInfo $myFileInfoObject = null;
    public static ?array $LMRClientTypeInfo = null;
    public static ?string $loanProgram = null;
    public static ?string $typeOfHMLOLoanRequesting = null;
    public static ?string $landValueCls = 'display: none;';
    public static ?string $landValueExtraCls = 'display: none;';
    public static ?string $HMLOPCElgibleState = null;
    public static ?array $warningErrorsArray = null;
    public static ?array $HMLOPCMinMaxLoanGuidelines = null;
    public static ?string $accrualType = null;
    public static ?float $maxLTV = null;
    public static ?float $minLoanAmount = null;
    public static ?float $maxLoanAmount = null;
    public static ?float $maxARV = null;
    public static ?float $minRate = null;
    public static ?float $maxRate = null;
    public static ?float $LGMaxLTC = null;
    public static ?float $minMidFico = null;
    public static ?float $maxMidFico = null;
    public static ?float $minPropertyForFixFlop = null;
    public static ?float $maxPropertyForFixFlop = null;
    public static ?float $minPropertyForGrndConst = null;
    public static ?float $maxPropertyForGrndConst = null;
    public static ?float $maxPoints = null;
    public static ?float $minPoints = null;
    public static ?float $downPaymentPercent = null;
    public static ?string $loanPgmDetails = null;
    public static ?string $reqForLoanProUnderwriting = null;
    public static ?string $guideLineMinSeasoningBusinessBankruptcyVal = null;
    public static ?string $guideLineMinSeasoningForeclosureVal = null;
    public static ?string $guideLineMinSeasoningPersonalBankruptcyVal = null;
    public static ?string $guideLineMinTimeVal = null;
    public static ?float $minDSCR = null;
    public static ?int $loanGuideLineId = null;
    public static ?int $disabledInputForClient = null;
    public static ?array $fileModuleInfo = null;
    public static ?array $fileSelectedModuleCode = null;
    public static ?array $servicesRequested = null;
    public static ?array $fileLP = null;
    public static ?int $tabIndex = null;
    public static ?array $fileMC = null;
    public static ?string $loanTermCRBStatus = null;
    public static ?string $amortizationType = null;
    public static ?string $isLoanPaymentAmt = null;
    public static ?string $lien1Terms = null;
    public static ?int $isHMLO = null;
    public static ?string $editIR = null;
    public static ?string $desiredCloseDate = null;
    public static ?string $loanTermExpireDate = null;
    public static ?string $purchaseCloseDate = null;
    public static ?string $resaleClosingDate = null;
    public static ?string $LOISentDate = null;
    public static ?string $trialPaymentDate1 = null;
    public static ?float $exitFeePoints = null;
    public static ?float $exitFeeAmount = null;
    public static ?float $prePaymentPenaltyPercentage = null;
    public static ?string $prePaymentSelectVal = null;
    public static ?array $prePaymentSelectValArr = null;
    public static ?string $isTherePrePaymentPenalty = null;
    public static ?string $prePaymentPenalty = null;
    public static ?string $prepayentSectionDisplay = 'display : none;';
    public static ?string $loanTerm = null;
    public static ?string $ownedSameEntity = null;
    public static ?string $paymentFrequency = null;
    public static ?string $docTypeLoanterms = null;
    public static ?int $noOfPropertiesAcquiring = null;
    public static ?int $includeTaxesInsuranceHOA = null;
    public static ?int $requiredConstruction = null;
    public static ?int $contingencyReserve = null;
    public static ?int $paymentReserves = null;
    public static ?float $prepaidInterestReserve = null;
    public static ?float $percentageTotalLoan = null;
    public static ?float $lotPurchasePrice = null;
    public static ?float $currentLotMarket = null;
    public static ?float $rehabTime = null;
    public static ?string $typeOfSale = null;
    public static ?string $lotStatus = null;
    public static ?string $isSubjectUnderConst = null;
    public static ?int $borNoOfSquareFeet = null;
    public static ?string $rehabToBeMade = null;
    public static ?float $initialAdvance = null;
    public static ?string $borComment = null;
    public static ?string $useOfFunds = null;
    public static ?string $loanGuarantee = null;
    public static ?string $assumability = null;
    public static ?string $limitedOrNot = null;
    public static ?string $prePayExcessOf20percent = null;
    public static ?string $isOwnLand = null;
    public static ?string $calcInrBasedOnMonthlyPayment = null;
    public static ?float $InrBasedOnMonthlyPayment = null;
    public static ?float $resalePrice = null;
    public static ?float $landValue = null;
    public static ?string $rateIndex = null;
    public static ?array $rateIndexArr = null;
    public static ?int $applicantConfirmed = null;
    public static ?string $balloonPayment = null;
    public static ?string $securityInstrument = null;
    public static ?string $HMLOEstateHeldIn = null;
    public static ?string $isBorPersonallyGuaranteeLoan = null;
    public static ?string $isBorBorrowedDownPayment = null;
    public static ?float $rentalIncomePerMonth = null;
    public static ?string $fileloanProgram = null;
    public static ?string $midFicoScore = null;
    public static ?int $borNoOfREPropertiesCompleted = null;
    public static ?int $borRehabPropCompleted = null;
    public static ?int $daysUntilClose = null;
    public static ?string $isThisGroundUpConstruction = null;
    public static ?string $insImpoundsMonth = null;
    public static ?float $insImpoundsMonthAmt = null;
    public static ?float $insImpoundsFee = null;
    public static ?float $taxImpoundsFee = null;
    public static ?string $isTaxesInsEscrowed = null;
    public static ?string $haveBorSquareFootage = null;
    public static ?string $additionalPropertyRestrictions = null;
    public static ?string $haveInterestreserve = null;
    public static ?float $closingCostFinanced = null;
    public static ?string $PCBorrCreditScoreRange = null;
    public static ?float $payOffMortgage1 = null;
    public static ?float $payOffMortgage2 = null;
    public static ?float $payOffOutstandingTaxes = null;
    public static ?float $payOffOtherOutstandingAmounts = null;
    public static ?float $cashOutAmt = null;
    public static ?string $interestChargedFromDate = null;
    public static ?string $interestChargedEndDate = null;
    public static ?string $propertyNeedRehab = null;
    public static ?string $exitStrategy = null;
    public static ?string $acceptedPurchase = null;
    public static ?string $haveCurrentLoanBal = null;
    public static ?string $haveInvoiceToFactor = null;
    public static ?string $isBlanketLoan = null;
    public static ?float $maxAmtToPutDown = null;
    public static ?string $taxes1 = null;
    public static ?string $doYouHaveInvoiceToFactor = null;
    public static ?float $originationPointsValue = null;
    public static ?float $brokerPointsValue = null;
    public static ?float $taxImpoundsMonthAmt = null;
    public static ?string $taxImpoundsMonth = null;
    public static ?int $isFeeUpdated = null;
    public static ?array $HMLOPCBasicLoanInfo = null;
    public static ?float $originationPointsRate = null;
    public static ?float $brokerPointsRate = null;
    public static ?float $escrowFees = null;
    public static ?float $closingCostFinancingFee = null;
    public static ?int $lienPosition = null;
    public static ?string $checkDisplayTermSheet = null;
    public static ?string $lenderNotes = null;

    public static ?float $applicationFee = null;
    public static ?float $estdTitleClosingFee = null;
    public static ?float $valuationBPOFee = null;
    public static ?float $creditReportFee = null;
    public static ?float $wireFee = null;
    public static ?float $floodCertificateFee = null;
    public static ?float $inspectionFees = null;
    public static ?float $dueDiligence = null;
    public static ?float $thirdPartyFees = null;
    public static ?float $underwritingFees = null;
    public static ?float $prePaidInterest = null;
    public static ?float $insurancePremium = null;
    public static ?float $wholeSaleAdminFee = null;

    public static ?float $attorneyFee = null;
    public static ?float $appraisalFee = null;
    public static ?float $processingFee = null;
    public static ?float $valuationAVMFee = null;
    public static ?float $backgroundCheckFee = null;
    public static ?float $documentPreparationFee = null;
    public static ?float $floodServiceFee = null;
    public static ?float $UccLienSearch = null;
    public static ?float $otherFee = null;
    public static ?float $recordingFee = null;
    public static ?float $propertyTax = null;
    public static ?float $realEstateTaxes = null;
    public static ?float $payOffLiensCreditors = null;
    public static ?float $survey = null;
    public static ?string $cityCountyTaxStamps = null;
    public static ?float $bufferAndMessengerFee = null;
    public static ?float $travelNotaryFee = null;
    public static ?float $drawsSetUpFee = null;
    public static ?float $drawsFee = null;
    public static ?float $valuationAVEFee = null;
    public static ?float $valuationCMAFee = null;
    public static ?float $taxServiceFee = null;
    public static ?float $servicingSetUpFee = null;
    public static ?float $projectFeasibility = null;
    public static ?float $wireTransferFeeToTitle = null;
    public static ?float $pastDuePropertyTaxes = null;
    public static ?float $wireTransferFeeToEscrow = null;
    public static ?float $employmentVerificationFee = null;
    public static ?float $taxReturnOrderFee = null;
    public static ?float $constructionHoldbackFee = null;
    public static ?float $creditCheckFee = null;
    public static ?float $sellerCreditsFee = null;


    public static ?string $involvedPurchase = null;
    public static ?string $areKnownHazards = null;
    public static ?string $areProReports = null;
    public static ?string $changeInCircumstance = null;
    public static ?string $changeDescription = null;
    public static ?string $restrictionsExplain = null;
    public static ?string $secondaryHolderName = null;
    public static ?float $wholesaleFee = null;
    public static ?float $secondaryFinancingAmount = null;

    public static ?string $isBorIntendToOccupyDispOpt = null;
    public static ?string $borrowingMoneyDispOpt = null;
    public static ?string $haveOwnershipInterestDispOpt = null;
    public static ?string $haveOwnershipInterest = null;
    public static ?string $borrowingMoney = null;
    public static ?string $purposeOfLoans = null;
    public static ?array $purposeOfLoan = null;
    public static ?string $typePropOwned = null;
    public static ?string $titleType = null;
    public static ?string $isBorIntendToOccupyPropAsPRI = null;
    public static ?string $isCoBorIntendToOccupyPropAsPRI = null;
    public static ?string $purposeOfLoanDisplayCls = null;
    public static ?float $desiredFundingAmount = null;
    public static ?string $loanMadeWholly = null;
    public static ?string $isSubjectSS = null;
    public static ?string $useOfProceeds = null;
    public static ?string $borBorrowedDownPaymentExpln = null;
    public static ?string $landAcquisition = null;

    public static ?string $newBuildingConstruction = null;
    public static ?string $constructionContingency = null;
    public static ?string $businessAcquisition = null;
    public static ?string $landAndBusinessAcquisition = null;
    public static ?string $buildingOrLeasehold = null;
    public static ?string $acquisitionOfMachineryEquipment = null;
    public static ?string $acquisitionOfFurnitureFixtures = null;
    public static ?string $inventoryPurchase = null;
    public static ?string $workingCapital = null;
    public static ?string $refinancingExistingBusinessDebt = null;
    public static ?string $franchiseFee = null;
    public static ?string $closingCost = null;
    public static ?string $otherCostText = null;
    public static ?string $otherCost = null;
    public static ?string $otherCostText2 = null;
    public static ?string $otherCost2 = null;
    public static ?string $estProjectCost = null;
    public static ?string $lessOwnerEquityToBeInjected = null;
    public static ?string $lessSellerCarryBack = null;
    public static ?string $loanRequestedForProject = null;
    public static ?float $miscellaneousFee = null;


    public static function init(?int $LMRId, ?int $PCID, ?int $userNumber)
    {
        self::$tabIndex = 1;
        self::$disabledInputForClient = 1;
        if (PageVariables::$userGroup == glUserGroup::CLIENT) self::$disabledInputForClient = 0;
        self::$myFileInfoObject = new myFileInfo();
        self::$myFileInfoObject->LMRId = $LMRId;
        self::$LMRClientTypeInfo = self::$myFileInfoObject->LMRClientTypeInfo() ?? [];
        if (self::$LMRClientTypeInfo) {
            self::$loanProgram = self::$LMRClientTypeInfo[0]->ClientType;
        }
        self::$typeOfHMLOLoanRequesting = self::$myFileInfoObject->fileHMLOPropertyInfo()->typeOfHMLOLoanRequesting;

        if (self::$loanProgram == 'CONS' ||
            self::$typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND) {
            self::$landValueCls = '';
        }
        if (self::$loanProgram == 'CONS'
            && (self::$typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                || self::$typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
                || self::$typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
                || self::$typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE)) {

            self::$landValueExtraCls = 'display: table-cell;';
        }
        self::$HMLOPCElgibleState = '';
        if (self::$loanProgram) {
            self::$HMLOPCMinMaxLoanGuidelines = getPCMinMaxLoanGuidelines::getReport([
                'loanPgm'     => self::$loanProgram,
                'PCID'        => $PCID,
                'loanPurpose' => '']);
            self::$warningErrorsArray = getPCHMLOBasicLoanInfoForFileLevel::getReport(['loanPgm' => self::$loanProgram, 'PCID' => $PCID]);
            $HMLOPCElgibleStateArray = self::$warningErrorsArray['HMLOPCElgibleState'];
            self::$HMLOPCElgibleState = (Arrays::implode2dArray(',', $HMLOPCElgibleStateArray, 'stateCode'));
        }

        self::$accrualType = accrualTypes::ACCRUAL_TYPE_30_360;
        self::$accrualType = self::$myFileInfoObject->fileHMLOPropertyInfo()->accrualType;
        if (!self::$accrualType) {
            self::$accrualType = accrualTypes::ACCRUAL_TYPE_30_360;
        }

        if (count(self::$HMLOPCMinMaxLoanGuidelines ?? [])) {

            self::$maxLTV = self::$HMLOPCMinMaxLoanGuidelines['maxLTV'];
            self::$minLoanAmount = self::$HMLOPCMinMaxLoanGuidelines['minLoanAmount'];
            self::$maxLoanAmount = self::$HMLOPCMinMaxLoanGuidelines['maxLoanAmount'];
            self::$maxARV = self::$HMLOPCMinMaxLoanGuidelines['maxLTVAfterRehab'];
            self::$minRate = self::$HMLOPCMinMaxLoanGuidelines['minRate'];
            self::$maxRate = self::$HMLOPCMinMaxLoanGuidelines['maxRate'];
            self::$LGMaxLTC = self::$HMLOPCMinMaxLoanGuidelines['totalLTC'];
            self::$minMidFico = self::$HMLOPCMinMaxLoanGuidelines['minMidFico'];
            self::$maxMidFico = self::$HMLOPCMinMaxLoanGuidelines['maxMidFico'];
            self::$minPropertyForFixFlop = self::$HMLOPCMinMaxLoanGuidelines['minPropertyForFixFlop'];
            self::$maxPropertyForFixFlop = self::$HMLOPCMinMaxLoanGuidelines['maxPropertyForFixFlop'];
            self::$minPropertyForGrndConst = self::$HMLOPCMinMaxLoanGuidelines['minPropertyForGrndConst'];
            self::$maxPropertyForGrndConst = self::$HMLOPCMinMaxLoanGuidelines['maxPropertyForGrndConst'];
            self::$maxPoints = self::$HMLOPCMinMaxLoanGuidelines['maxPoints'];
            self::$minPoints = self::$HMLOPCMinMaxLoanGuidelines['minPoints'];
            self::$downPaymentPercent = (float)self::$HMLOPCMinMaxLoanGuidelines['downPaymentPercentage'];
            self::$loanPgmDetails = urldecode(self::$HMLOPCMinMaxLoanGuidelines['loanPgmDetails']);
            self::$reqForLoanProUnderwriting = urldecode(self::$HMLOPCMinMaxLoanGuidelines['reqForLoanProUnderwriting']);

            self::$guideLineMinSeasoningBusinessBankruptcyVal = self::$HMLOPCMinMaxLoanGuidelines['MinSeasoningBusinessBankruptcyVal'];
            self::$guideLineMinSeasoningForeclosureVal = self::$HMLOPCMinMaxLoanGuidelines['MinSeasoningForeclosureVal'];
            self::$guideLineMinSeasoningPersonalBankruptcyVal = self::$HMLOPCMinMaxLoanGuidelines['MinSeasoningPersonalBankruptcyVal'];
            self::$guideLineMinTimeVal = self::$HMLOPCMinMaxLoanGuidelines['minTimeVal'];
            self::$minDSCR = self::$HMLOPCMinMaxLoanGuidelines['minDSCR'];
            self::$loanGuideLineId = self::$HMLOPCMinMaxLoanGuidelines['BLID'];
        }
        self::$fileModuleInfo = fileModuleInfo::getReport($LMRId);
        self::$fileSelectedModuleCode = self::$fileModuleInfo['fileSelectedModuleCode'];
        self::$fileMC = self::$fileSelectedModuleCode;
        self::$servicesRequested = branchClientTypeInfo::getReport(LMRequest::$activeTab, LMRequest::myFileInfo()->LMRInfo()->FBRID, self::$fileSelectedModuleCode);
        foreach (self::$LMRClientTypeInfo as $item) {
            self::$fileLP[] = trim($item->ClientType);
        }
        self::$fileloanProgram = self::$LMRClientTypeInfo[0]->ClientType;
        if (in_array($PCID, [glPCID::PCID_CRB, glPCID::PCID_DEV_DAVE])) {
            if (LMRequest::$activeTab == 'HMLI') {
                if (!loanSetting::$loanSetting['totalTerms']) {
                    loanSetting::init($LMRId);
                }
                $CRB_totalTerms = loanSetting::$loanSetting['totalTerms'];
                $isAdjustable = loanSetting::$loanSetting['isAdjustable'];
                if ($isAdjustable != 'Fixed Rate') self::$loanTermCRBStatus = ' disabled ';
            }
            if (LMRequest::$activeTab == 'FA') {
                self::$loanTermCRBStatus = ' disabled ';
            }
        }
        self::$amortizationType = self::$myFileInfoObject->fileHMLONewLoanInfo()->amortizationType;
        self::$isLoanPaymentAmt = self::$myFileInfoObject->fileHMLONewLoanInfo()->isLoanPaymentAmt;
        if(empty(self::$isLoanPaymentAmt)){
            self::$isLoanPaymentAmt = paymentBased::TOTAL_LOAN_AMOUNT;
        }
        self::$lien1Terms = LMRequest::myFileInfo()->LMRInfo()->lien1Terms;
        if (in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
            self::$lien1Terms = LMRequest::myFileInfo()->LMRInfo()->lien1Terms;
        } else {
            //other PCs
            if (self::$isHMLO == 1 && self::$lien1Terms == '') {
                self::$lien1Terms = LoanTerms::INTEREST_ONLY;
            }
        }
        if (self::$isLoanPaymentAmt == paymentBased::SET_MANUAL_PAYMENT) {
            self::$lien1Terms = '';
        }

        if ((PageVariables::$userGroup == 'Branch'
                && LMRequest::myFileInfo()->BranchInfo()->allowEditToIR != 1)
            || (PageVariables::$userGroup == 'Agent'  && !PageVariables::$externalBroker && LMRequest::myFileInfo()->BrokerInfo()->allowEditToIR != 1)
        || (PageVariables::$userGroup == 'Agent' && PageVariables::$externalBroker && LMRequest::myFileInfo()->SecondaryBrokerInfo()->allowEditToIR != 1)) {
            self::$editIR = ' readonly ';
        }

        if (Dates::IsEmpty(LMRequest::myFileInfo()->QAInfo()->desiredClosingDate)) {
            self::$desiredCloseDate = '';
        } else {
            self::$desiredCloseDate = Dates::formatDateWithRE(LMRequest::myFileInfo()->QAInfo()->desiredClosingDate, 'YMD', 'm/d/Y');
        }

        if (Dates::IsEmpty(self::$myFileInfoObject->fileHMLONewLoanInfo()->loanTermExpireDate)) {
            self::$loanTermExpireDate = '';
        } else {
            self::$loanTermExpireDate = Dates::formatDateWithRE(self::$myFileInfoObject->fileHMLONewLoanInfo()->loanTermExpireDate, 'YMD', 'm/d/Y');
        }

        if (Dates::IsEmpty(LMRequest::myFileInfo()->QAInfo()->closingDate)) {
            self::$purchaseCloseDate = '';
        } else {
            self::$purchaseCloseDate = Dates::formatDateWithRE(LMRequest::myFileInfo()->QAInfo()->closingDate, 'YMD', 'm/d/Y');
        }

        if (Dates::IsEmpty(self::$myFileInfoObject->fileHMLONewLoanInfo()->resaleClosingDate)) {
            self::$resaleClosingDate = '';
        } else {
            self::$resaleClosingDate = Dates::formatDateWithRE(self::$myFileInfoObject->fileHMLONewLoanInfo()->resaleClosingDate, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty(self::$myFileInfoObject->fileHMLONewLoanInfo()->LOISentDate)) {
            self::$LOISentDate = '';
        } else {
            self::$LOISentDate = Dates::formatDateWithRE(self::$myFileInfoObject->fileHMLONewLoanInfo()->LOISentDate, 'YMD', 'm/d/Y');
        }

        $dateDiff = [];
        $dateDiff['lastPaymentMade'] = date('Y-m-d');
        $dateDiff['futureDate'] = self::$purchaseCloseDate;
        self::$daysUntilClose = Integers::calculateNoOfDaysBehind($dateDiff);
        if (self::$daysUntilClose < 0) {
            self::$daysUntilClose = null;
        }

        self::$trialPaymentDate1 = Dates::formatDateWithRE(LMRequest::myFileInfo()->fileResponse()->trialPaymentDate1, 'YMD', 'm/d/Y');
        self::$exitFeePoints = self::$myFileInfoObject->fileHMLONewLoanInfo()->exitFeePoints;
        self::$exitFeeAmount = self::$myFileInfoObject->fileHMLONewLoanInfo()->exitFeeAmount;
        self::$initialAdvance = self::$myFileInfoObject->fileHMLONewLoanInfo()->initialAdvance;
        self::$prePaymentPenaltyPercentage = self::$myFileInfoObject->fileHMLONewLoanInfo()->prePaymentPenaltyPercentage;
        self::$prepaidInterestReserve = self::$myFileInfoObject->fileHMLONewLoanInfo()->prepaidInterestReserve;
        self::$borNoOfSquareFeet = self::$myFileInfoObject->fileHMLONewLoanInfo()->borNoOfSquareFeet;
        self::$prePaymentSelectVal = self::$myFileInfoObject->fileHMLONewLoanInfo()->prePaymentSelectVal;
        self::$prePaymentSelectValArr = explode(',', self::$prePaymentSelectVal);
        self::$noOfPropertiesAcquiring = self::$myFileInfoObject->fileHMLONewLoanInfo()->noOfPropertiesAcquiring;
        self::$ownedSameEntity = self::$myFileInfoObject->fileHMLONewLoanInfo()->ownedSameEntity;
        self::$paymentFrequency = self::$myFileInfoObject->fileHMLONewLoanInfo()->paymentFrequency;
        self::$assumability = self::$myFileInfoObject->fileHMLONewLoanInfo()->assumability;
        self::$isOwnLand = self::$myFileInfoObject->fileHMLONewLoanInfo()->isOwnLand;
        self::$applicantConfirmed = self::$myFileInfoObject->fileHMLONewLoanInfo()->applicantConfirmed;
        self::$calcInrBasedOnMonthlyPayment = self::$myFileInfoObject->fileHMLONewLoanInfo()->calcInrBasedOnMonthlyPayment;
        self::$InrBasedOnMonthlyPayment = self::$myFileInfoObject->fileHMLONewLoanInfo()->InrBasedOnMonthlyPayment;
        self::$resalePrice = self::$myFileInfoObject->fileHMLONewLoanInfo()->resalePrice;
        self::$landValue = self::$myFileInfoObject->fileHMLONewLoanInfo()->landValue;
        self::$insImpoundsMonth = self::$myFileInfoObject->fileHMLONewLoanInfo()->insImpoundsMonth;
        self::$insImpoundsMonthAmt = self::$myFileInfoObject->fileHMLONewLoanInfo()->insImpoundsMonthAmt;
        self::$insImpoundsFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->insImpoundsFee;
        self::$taxImpoundsFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->taxImpoundsFee;
        self::$closingCostFinanced = self::$myFileInfoObject->fileHMLONewLoanInfo()->closingCostFinanced;
        self::$payOffMortgage1 = self::$myFileInfoObject->fileHMLONewLoanInfo()->payOffMortgage1;
        self::$payOffMortgage2 = self::$myFileInfoObject->fileHMLONewLoanInfo()->payOffMortgage2;
        self::$payOffOutstandingTaxes = self::$myFileInfoObject->fileHMLONewLoanInfo()->payOffOutstandingTaxes;
        self::$payOffOtherOutstandingAmounts = self::$myFileInfoObject->fileHMLONewLoanInfo()->payOffOtherOutstandingAmounts;
        self::$cashOutAmt = self::$myFileInfoObject->fileHMLONewLoanInfo()->cashOutAmt;
        self::$isTaxesInsEscrowed = self::$myFileInfoObject->fileHMLONewLoanInfo()->isTaxesInsEscrowed;
        self::$haveBorSquareFootage = self::$myFileInfoObject->fileHMLONewLoanInfo()->haveBorSquareFootage;
        self::$additionalPropertyRestrictions = self::$myFileInfoObject->fileHMLONewLoanInfo()->additionalPropertyRestrictions;
        self::$haveInterestreserve = self::$myFileInfoObject->fileHMLONewLoanInfo()->haveInterestreserve;
        if (self::$haveInterestreserve == '') {
            self::$haveInterestreserve = 'No';
        }


        if (Dates::IsEmpty(self::$myFileInfoObject->fileHMLONewLoanInfo()->interestChargedFromDate)) {
            self::$interestChargedFromDate = '';
        } else {
            self::$interestChargedFromDate = Dates::formatDateWithRE(self::$myFileInfoObject->fileHMLONewLoanInfo()->interestChargedFromDate, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty(self::$myFileInfoObject->fileHMLONewLoanInfo()->interestChargedEndDate)) {
            self::$interestChargedEndDate = '';
        } else {
            self::$interestChargedEndDate = Dates::formatDateWithRE(self::$myFileInfoObject->fileHMLONewLoanInfo()->interestChargedEndDate, 'YMD', 'm/d/Y');
        }


        self::$originationPointsValue = self::$myFileInfoObject->fileHMLONewLoanInfo()->originationPointsValue;
        self::$brokerPointsValue = self::$myFileInfoObject->fileHMLONewLoanInfo()->brokerPointsValue;
        self::$taxImpoundsMonth = self::$myFileInfoObject->fileHMLONewLoanInfo()->taxImpoundsMonth;
        self::$taxImpoundsMonthAmt = self::$myFileInfoObject->fileHMLONewLoanInfo()->taxImpoundsMonthAmt;
        self::$isFeeUpdated = self::$myFileInfoObject->fileHMLONewLoanInfo()->isFeeUpdated;
        self::$originationPointsRate = self::$myFileInfoObject->fileHMLONewLoanInfo()->originationPointsRate;
        self::$brokerPointsRate = self::$myFileInfoObject->fileHMLONewLoanInfo()->brokerPointsRate;
        self::$escrowFees = self::$myFileInfoObject->fileHMLONewLoanInfo()->escrowFees;
        self::$closingCostFinancingFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->closingCostFinancingFee;


        self::$applicationFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->applicationFee;
        self::$estdTitleClosingFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->estdTitleClosingFee;
        self::$valuationBPOFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->valuationBPOFee;
        self::$creditReportFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->creditReportFee;
        self::$wireFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->wireFee;
        self::$floodCertificateFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->floodCertificateFee;
        self::$inspectionFees = self::$myFileInfoObject->fileHMLONewLoanInfo()->inspectionFees;
        self::$dueDiligence = self::$myFileInfoObject->fileHMLONewLoanInfo()->dueDiligence;
        self::$thirdPartyFees = self::$myFileInfoObject->fileHMLONewLoanInfo()->thirdPartyFees;
        self::$underwritingFees = self::$myFileInfoObject->fileHMLONewLoanInfo()->underwritingFees;
        self::$prePaidInterest = self::$myFileInfoObject->fileHMLONewLoanInfo()->prePaidInterest;
        self::$insurancePremium = self::$myFileInfoObject->fileHMLONewLoanInfo()->insurancePremium;
        self::$wholeSaleAdminFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->wholeSaleAdminFee;


        self::$attorneyFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->attorneyFee;
        self::$appraisalFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->appraisalFee;
        self::$processingFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->processingFee;
        self::$valuationAVMFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->valuationAVMFee;
        self::$backgroundCheckFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->backgroundCheckFee;
        self::$documentPreparationFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->documentPreparationFee;
        self::$floodServiceFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->floodServiceFee;
        self::$UccLienSearch = self::$myFileInfoObject->fileHMLONewLoanInfo()->UccLienSearch;
        self::$otherFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->otherFee;
        self::$recordingFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->recordingFee;
        self::$propertyTax = self::$myFileInfoObject->fileHMLONewLoanInfo()->propertyTax;
        self::$realEstateTaxes = self::$myFileInfoObject->fileHMLONewLoanInfo()->realEstateTaxes;
        self::$payOffLiensCreditors = self::$myFileInfoObject->fileHMLONewLoanInfo()->payOffLiensCreditors;
        self::$survey = self::$myFileInfoObject->fileHMLONewLoanInfo()->survey;
        self::$cityCountyTaxStamps = self::$myFileInfoObject->fileHMLONewLoanInfo()->cityCountyTaxStamps;
        self::$bufferAndMessengerFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->bufferAndMessengerFee;
        self::$travelNotaryFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->travelNotaryFee;
        self::$drawsSetUpFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->drawsSetUpFee;


        self::$drawsFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->drawsFee;
        self::$valuationAVEFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->valuationAVEFee;
        self::$valuationCMAFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->valuationCMAFee;
        self::$taxServiceFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->taxServiceFee;
        self::$servicingSetUpFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->servicingSetUpFee;
        self::$projectFeasibility = self::$myFileInfoObject->fileHMLONewLoanInfo()->projectFeasibility;
        self::$wireTransferFeeToTitle = self::$myFileInfoObject->fileHMLONewLoanInfo()->wireTransferFeeToTitle;
        self::$pastDuePropertyTaxes = self::$myFileInfoObject->fileHMLONewLoanInfo()->pastDuePropertyTaxes;
        self::$wireTransferFeeToEscrow = self::$myFileInfoObject->fileHMLONewLoanInfo()->wireTransferFeeToEscrow;
        self::$employmentVerificationFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->employmentVerificationFee;
        self::$taxReturnOrderFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->taxReturnOrderFee;
        self::$constructionHoldbackFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->constructionHoldbackFee;
        self::$creditCheckFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->creditCheckFee;
        self::$sellerCreditsFee = self::$myFileInfoObject->fileHMLONewLoanInfo()->sellerCreditsFee;

        self::$isTherePrePaymentPenalty = self::$myFileInfoObject->fileHMLOPropertyInfo()->isTherePrePaymentPenalty;
        self::$prePaymentPenalty = self::$myFileInfoObject->fileHMLOPropertyInfo()->prePaymentPenalty;
        self::$loanTerm = self::$myFileInfoObject->fileHMLOPropertyInfo()->loanTerm;
        if (self::$isHMLO == 1 && !self::$prePaymentPenalty) self::$prePaymentPenalty = 'None';
        if (self::$isTherePrePaymentPenalty == 'Yes') self::$prepayentSectionDisplay = 'display: block;';
        self::$includeTaxesInsuranceHOA = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->includeTaxesInsuranceHOA;
        self::$requiredConstruction = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->requiredConstruction;
        self::$contingencyReserve = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->contingencyReserve;
        self::$paymentReserves = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->paymentReserves;
        self::$percentageTotalLoan = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->percentageTotalLoan;
        self::$typeOfSale = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typeOfSale;
        self::$lotStatus = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lotStatus;
        self::$lotPurchasePrice = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lotPurchasePrice;
        self::$currentLotMarket = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->currentLotMarket;
        self::$isSubjectUnderConst = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->isSubjectUnderConst;
        self::$rehabToBeMade = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->rehabToBeMade;
        self::$rehabTime = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->rehabTime;
        self::$docTypeLoanterms = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->docType;
        self::$useOfFunds = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->useOfFunds;
        self::$loanGuarantee = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanGuaranteeType;
        self::$rentalIncomePerMonth = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->rentalIncomePerMonth;
        self::$securityInstrument = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->securityInstrument;
        self::$HMLOEstateHeldIn = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->HMLOEstateHeldIn;
        self::$balloonPayment = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->balloonPayment;
        self::$prePayExcessOf20percent = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->prePayExcessOf20percent;
        self::$limitedOrNot = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->limitedOrNot;
        self::$isThisGroundUpConstruction = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->isThisGroundUpConstruction;
        self::$rateIndex = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->rateIndex;
        self::$rateIndexArr = explode(',', self::$rateIndex);
        self::$propertyNeedRehab = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->propertyNeedRehab;
        self::$lotPurchasePrice = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lotPurchasePrice;
        self::$currentLotMarket = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->currentLotMarket;
        self::$exitStrategy = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->exitStrategy;
        self::$acceptedPurchase = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->acceptedPurchase;
        self::$haveCurrentLoanBal = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->haveCurrentLoanBal;
        self::$doYouHaveInvoiceToFactor = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->haveInvoiceToFactor;
        self::$maxAmtToPutDown = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->maxAmtToPutDown;
        self::$isBlanketLoan = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->isBlanketLoan;
        self::$lienPosition = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lienPosition;
        self::$checkDisplayTermSheet = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->checkDisplayTermSheet;
        self::$lenderNotes = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->lenderNotes;
        self::$involvedPurchase = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->involvedPurchase;
        self::$wholesaleFee = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->wholesaleFee;
        self::$areKnownHazards = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->areKnownHazards;
        self::$areProReports = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->areProReports;
        self::$changeInCircumstance = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->changeInCircumstance;
        self::$changeDescription = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->changeDescription;
        self::$restrictionsExplain = LMRequest::myFileInfo()->fileHMLONewLoanInfo()->restrictionsExplain;
        self::$secondaryHolderName = LMRequest::myFileInfo()->fileHMLONewLoanInfo()->secondaryHolderName;
        self::$secondaryFinancingAmount = LMRequest::myFileInfo()->fileHMLONewLoanInfo()->secondaryFinancingAmount;


        self::$borrowingMoney = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->borrowingMoney;
        self::$purposeOfLoans = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->purposeOfLoan;
        if (self::$purposeOfLoans != '') {
            self::$purposeOfLoan = explode('~', self::$purposeOfLoans) ?? [];
        }

        if (in_array('Other', self::$purposeOfLoan ?? [])) {
            self::$purposeOfLoanDisplayCls = '';
        } else {
            self::$purposeOfLoanDisplayCls = 'd-none';
        }
        self::$typePropOwned = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->typePropOwned;
        self::$titleType = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->titleType;
        self::$haveOwnershipInterest = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->haveOwnershipInterest;

        self::$desiredFundingAmount = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->desiredFundingAmount;
        self::$loanMadeWholly = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanMadeWholly;
        self::$isSubjectSS = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->isSubjectSS;
        self::$useOfProceeds = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->useOfProceeds;

        self:: $borrowingMoneyDispOpt = self::$borrowingMoney == 'Yes' ? 'display: block;' : 'display: none;';
        self:: $haveOwnershipInterestDispOpt = self::$haveOwnershipInterest == 'Yes' ? 'display: block;' : 'display: none;';


        self::$borComment = LMRequest::myFileInfo()->LOExplanationInfo()->borComment;

        self::$isBorPersonallyGuaranteeLoan = LMRequest::myFileInfo()->fileHMLOBackGroundInfo()->isBorPersonallyGuaranteeLoan;
        self::$isBorBorrowedDownPayment = LMRequest::myFileInfo()->fileHMLOBackGroundInfo()->isBorBorrowedDownPayment;
        self::$isBorIntendToOccupyPropAsPRI = LMRequest::myFileInfo()->fileHMLOBackGroundInfo()->isBorIntendToOccupyPropAsPRI;
        self::$borBorrowedDownPaymentExpln = LMRequest::myFileInfo()->fileHMLOBackGroundInfo()->borBorrowedDownPaymentExpln;
        self::$isCoBorIntendToOccupyPropAsPRI = LMRequest::myFileInfo()->fileHMLOBackGroundInfo()->isCoBorIntendToOccupyPropAsPRI;
        self:: $isBorIntendToOccupyDispOpt = self::$isBorIntendToOccupyPropAsPRI == 'Yes' ? 'display: block;' : 'display: none;';

        self::$midFicoScore = LMRequest::myFileInfo()->fileHMLOInfo()->midFicoScore;

        self::$borNoOfREPropertiesCompleted = LMRequest::myFileInfo()->getFileHMLOExperience()->borNoOfREPropertiesCompleted;
        self::$borRehabPropCompleted = LMRequest::myFileInfo()->getFileHMLOExperience()->borRehabPropCompleted;
        self::$landAcquisition = LMRequest::myFileInfo()->estimatedProjectCost()->landAcquisition;

        $epcArray = BaseHTML::sectionAccess(['sId' => 'EPC', 'opt' => LMRequest::$activeTab]);
        if (BaseHTML::fieldAccess(['fNm' => 'landAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$landAcquisition = LMRequest::myFileInfo()->estimatedProjectCost()->landAcquisition;
        if (BaseHTML::fieldAccess(['fNm' => 'newBuildingConstruction', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$newBuildingConstruction = LMRequest::myFileInfo()->estimatedProjectCost()->newBuildingConstruction;
        if (BaseHTML::fieldAccess(['fNm' => 'constructionContingency', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$constructionContingency = LMRequest::myFileInfo()->estimatedProjectCost()->constructionContingency;
        if (BaseHTML::fieldAccess(['fNm' => 'businessAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$businessAcquisition = LMRequest::myFileInfo()->estimatedProjectCost()->businessAcquisition;
        if (BaseHTML::fieldAccess(['fNm' => 'landAndBusinessAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$landAndBusinessAcquisition = LMRequest::myFileInfo()->estimatedProjectCost()->landAndBusinessAcquisition;
        if (BaseHTML::fieldAccess(['fNm' => 'buildingOrLeasehold', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$buildingOrLeasehold = LMRequest::myFileInfo()->estimatedProjectCost()->buildingOrLeasehold;
        if (BaseHTML::fieldAccess(['fNm' => 'acquisitionOfMachineryEquipment', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$acquisitionOfMachineryEquipment = LMRequest::myFileInfo()->estimatedProjectCost()->acquisitionOfMachineryEquipment;
        if (BaseHTML::fieldAccess(['fNm' => 'acquisitionOfFurnitureFixtures', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$acquisitionOfFurnitureFixtures = LMRequest::myFileInfo()->estimatedProjectCost()->acquisitionOfFurnitureFixtures;
        if (BaseHTML::fieldAccess(['fNm' => 'inventoryPurchase', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$inventoryPurchase = LMRequest::myFileInfo()->estimatedProjectCost()->inventoryPurchase;
        if (BaseHTML::fieldAccess(['fNm' => 'workingCapital', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$workingCapital = LMRequest::myFileInfo()->estimatedProjectCost()->workingCapital;
        if (BaseHTML::fieldAccess(['fNm' => 'refinancingExistingBusinessDebt', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$refinancingExistingBusinessDebt = LMRequest::myFileInfo()->estimatedProjectCost()->refinancingExistingBusinessDebt;
        if (BaseHTML::fieldAccess(['fNm' => 'franchiseFee', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$franchiseFee = LMRequest::myFileInfo()->estimatedProjectCost()->franchiseFee;
        if (BaseHTML::fieldAccess(['fNm' => 'closingCost', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$closingCost = LMRequest::myFileInfo()->estimatedProjectCost()->closingCost;
        self::$otherCostText = LMRequest::myFileInfo()->estimatedProjectCost()->otherCostText;
        if (BaseHTML::fieldAccess(['fNm' => 'otherCost', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$otherCost = LMRequest::myFileInfo()->estimatedProjectCost()->otherCost;
        self::$otherCostText2 = LMRequest::myFileInfo()->estimatedProjectCost()->otherCostText2;
        if (BaseHTML::fieldAccess(['fNm' => 'otherCost2', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$otherCost2 = LMRequest::myFileInfo()->estimatedProjectCost()->otherCost2;
        if (BaseHTML::fieldAccess(['fNm' => 'lessOwnerEquityToBeInjected', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$lessOwnerEquityToBeInjected = LMRequest::myFileInfo()->estimatedProjectCost()->lessOwnerEquityToBeInjected;
        if (BaseHTML::fieldAccess(['fNm' => 'lessSellerCarryBack', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) self::$lessSellerCarryBack = LMRequest::myFileInfo()->estimatedProjectCost()->lessSellerCarryBack;

        $estimatedProjectCost = Strings::replaceCommaValues(self::$landAcquisition)
            + Strings::replaceCommaValues(self::$newBuildingConstruction)
            + Strings::replaceCommaValues(self::$constructionContingency);

        self::$estProjectCost = round($estimatedProjectCost, 2);

        self::$loanRequestedForProject = ($estimatedProjectCost - (Strings::replaceCommaValues(self::$lessOwnerEquityToBeInjected) + Strings::replaceCommaValues(self::$lessSellerCarryBack)));
        self::$loanRequestedForProject = round(self::$loanRequestedForProject, 2);

        self::$taxes1 = LMRequest::myFileInfo()->incomeInfo()->taxes1;
        $clientTypes = LMRequest::myFileInfo()->ClientTypes() ?? [];
        self::$HMLOPCBasicLoanInfo = LMRequest::myFileInfo()->HMLOPCBasicLoanInfo($clientTypes) ? (LMRequest::myFileInfo()->HMLOPCBasicLoanInfo($clientTypes)->toArray() ?? []) : [];
        if (count(self::$HMLOPCBasicLoanInfo) > 0 && self::$isFeeUpdated != 1) {
            self::$originationPointsValue = Strings::checkAndUpdateValue(self::$originationPointsValue, self::$HMLOPCBasicLoanInfo['originationPointsValue']);
            self::$brokerPointsValue = Strings::checkAndUpdateValue(self::$brokerPointsValue, self::$HMLOPCBasicLoanInfo['brokerPointsValue']);
            self::$taxImpoundsMonth = Strings::checkAndUpdateValue(self::$taxImpoundsMonth, self::$HMLOPCBasicLoanInfo['taxImpoundsMonth']);
            self::$taxImpoundsMonthAmt = Strings::checkAndUpdateValue(self::$taxImpoundsMonthAmt, self::$HMLOPCBasicLoanInfo['taxImpoundsMonthAmt']);
            self::$insImpoundsMonthAmt = Strings::checkAndUpdateValue(self::$insImpoundsMonthAmt, self::$HMLOPCBasicLoanInfo['insImpoundsMonthAmt']);
            self::$insImpoundsFee = Strings::checkAndUpdateValue(self::$insImpoundsFee, self::$HMLOPCBasicLoanInfo['insImpoundsFee']);

            self::$minRate = Strings::checkAndUpdateValue(self::$minRate, self::$HMLOPCBasicLoanInfo['minRate']);
            self::$maxRate = Strings::checkAndUpdateValue(self::$maxRate, self::$HMLOPCBasicLoanInfo['maxRate']);
            self::$originationPointsRate = Strings::checkAndUpdateValue(self::$originationPointsRate, self::$HMLOPCBasicLoanInfo['originationPointsRate']);
            self::$processingFee = Strings::checkAndUpdateValue(self::$processingFee, self::$HMLOPCBasicLoanInfo['processingFee']);
            self::$brokerPointsRate = Strings::checkAndUpdateValue(self::$brokerPointsRate, self::$HMLOPCBasicLoanInfo['brokerPointsRate']);
            self::$appraisalFee = Strings::checkAndUpdateValue(self::$appraisalFee, self::$HMLOPCBasicLoanInfo['appraisalFee']);
            self::$applicationFee = Strings::checkAndUpdateValue(self::$applicationFee, self::$HMLOPCBasicLoanInfo['applicationFee']);
            self::$drawsSetUpFee = Strings::checkAndUpdateValue(self::$drawsSetUpFee, self::$HMLOPCBasicLoanInfo['drawsSetUpFee']);
            self::$estdTitleClosingFee = Strings::checkAndUpdateValue(self::$estdTitleClosingFee, self::$HMLOPCBasicLoanInfo['estdTitleClosingFee']);
            self::$miscellaneousFee = Strings::checkAndUpdateValue(self::$miscellaneousFee, self::$HMLOPCBasicLoanInfo['miscellaneousFee']);
            self::$closingCostFinanced = Strings::checkAndUpdateValue(self::$closingCostFinanced, self::$HMLOPCBasicLoanInfo['closingCostFinanced']);
            self::$PCBorrCreditScoreRange = Strings::checkAndUpdateValue(self::$PCBorrCreditScoreRange, self::$HMLOPCBasicLoanInfo['PCBorrCreditScoreRange']);
            self::$drawsFee = Strings::checkAndUpdateValue(self::$drawsFee, self::$HMLOPCBasicLoanInfo['drawsFee']);

            self::$valuationBPOFee = Strings::checkAndUpdateValue(self::$valuationBPOFee, self::$HMLOPCBasicLoanInfo['valuationBPOFee']);
            self::$valuationAVMFee = Strings::checkAndUpdateValue(self::$valuationAVMFee, self::$HMLOPCBasicLoanInfo['valuationAVMFee']);
            self::$creditReportFee = Strings::checkAndUpdateValue(self::$creditReportFee, self::$HMLOPCBasicLoanInfo['creditReportFee']);
            self::$backgroundCheckFee = Strings::checkAndUpdateValue(self::$backgroundCheckFee, self::$HMLOPCBasicLoanInfo['backgroundCheckFee']);
            self::$floodCertificateFee = Strings::checkAndUpdateValue(self::$floodCertificateFee, self::$HMLOPCBasicLoanInfo['floodCertificateFee']);
            self::$documentPreparationFee = Strings::checkAndUpdateValue(self::$documentPreparationFee, self::$HMLOPCBasicLoanInfo['documentPreparationFee']);
            self::$wireFee = Strings::checkAndUpdateValue(self::$wireFee, self::$HMLOPCBasicLoanInfo['wireFee']);
            self::$servicingSetUpFee = Strings::checkAndUpdateValue(self::$servicingSetUpFee, self::$HMLOPCBasicLoanInfo['servicingSetUpFee']);
            self::$taxServiceFee = Strings::checkAndUpdateValue(self::$taxServiceFee, self::$HMLOPCBasicLoanInfo['taxServiceFee']);
            self::$floodServiceFee = Strings::checkAndUpdateValue(self::$floodServiceFee, self::$HMLOPCBasicLoanInfo['floodServiceFee']);
            self::$inspectionFees = Strings::checkAndUpdateValue(self::$inspectionFees, self::$HMLOPCBasicLoanInfo['inspectionFees']);
            self::$projectFeasibility = Strings::checkAndUpdateValue(self::$projectFeasibility, self::$HMLOPCBasicLoanInfo['projectFeasibility']);
            self::$dueDiligence = Strings::checkAndUpdateValue(self::$dueDiligence, self::$HMLOPCBasicLoanInfo['dueDiligence']);
            self::$UccLienSearch = Strings::checkAndUpdateValue(self::$UccLienSearch, self::$HMLOPCBasicLoanInfo['UccLienSearch']);
            self::$otherFee = Strings::checkAndUpdateValue(self::$otherFee, self::$HMLOPCBasicLoanInfo['otherFee']);
            self::$taxImpoundsFee = Strings::checkAndUpdateValue(self::$taxImpoundsFee, self::$HMLOPCBasicLoanInfo['taxImpoundsFee']);
            self::$insImpoundsFee = Strings::checkAndUpdateValue(self::$insImpoundsFee, self::$HMLOPCBasicLoanInfo['insImpoundsFee']);
            self::$thirdPartyFees = Strings::checkAndUpdateValue(self::$thirdPartyFees, self::$HMLOPCBasicLoanInfo['thirdPartyFees']);
            self::$closingCostFinancingFee = Strings::checkAndUpdateValue(self::$closingCostFinancingFee, self::$HMLOPCBasicLoanInfo['closingCostFinancingFee']);
            self::$attorneyFee = Strings::checkAndUpdateValue(self::$attorneyFee, self::$HMLOPCBasicLoanInfo['attorneyFee']);
            self::$escrowFees = Strings::checkAndUpdateValue(self::$escrowFees, self::$HMLOPCBasicLoanInfo['escrowFees']);
            self::$recordingFee = Strings::checkAndUpdateValue(self::$recordingFee, self::$HMLOPCBasicLoanInfo['recordingFee']);
            self::$underwritingFees = Strings::checkAndUpdateValue(self::$underwritingFees, self::$HMLOPCBasicLoanInfo['underwritingFees']);
            self::$propertyTax = Strings::checkAndUpdateValue(self::$propertyTax, self::$HMLOPCBasicLoanInfo['propertyTax']);
            self::$prePaidInterest = Strings::checkAndUpdateValue(self::$prePaidInterest, self::$HMLOPCBasicLoanInfo['prePaidInterest']);
            self::$realEstateTaxes = Strings::checkAndUpdateValue(self::$realEstateTaxes, self::$HMLOPCBasicLoanInfo['realEstateTaxes']);
            self::$insurancePremium = Strings::checkAndUpdateValue(self::$insurancePremium, self::$HMLOPCBasicLoanInfo['insurancePremium']);
            self::$payOffLiensCreditors = Strings::checkAndUpdateValue(self::$payOffLiensCreditors, self::$HMLOPCBasicLoanInfo['payOffLiensCreditors']);
            self::$bufferAndMessengerFee = Strings::checkAndUpdateValue(self::$bufferAndMessengerFee, self::$HMLOPCBasicLoanInfo['bufferAndMessengerFee']);
            self::$travelNotaryFee = Strings::checkAndUpdateValue(self::$travelNotaryFee, self::$HMLOPCBasicLoanInfo['travelNotaryFee']);
            self::$wireTransferFeeToTitle = Strings::checkAndUpdateValue(self::$wireTransferFeeToTitle, self::$HMLOPCBasicLoanInfo['wireTransferFeeToTitle']);
            self::$wireTransferFeeToEscrow = Strings::checkAndUpdateValue(self::$wireTransferFeeToEscrow, self::$HMLOPCBasicLoanInfo['wireTransferFeeToEscrow']);
            self::$pastDuePropertyTaxes = Strings::checkAndUpdateValue(self::$pastDuePropertyTaxes, self::$HMLOPCBasicLoanInfo['pastDuePropertyTaxes']);
            self::$survey = Strings::checkAndUpdateValue(self::$survey, self::$HMLOPCBasicLoanInfo['survey']);
            self::$wholeSaleAdminFee = Strings::checkAndUpdateValue(self::$wholeSaleAdminFee, self::$HMLOPCBasicLoanInfo['wholeSaleAdminFee']);
        }
    }
}