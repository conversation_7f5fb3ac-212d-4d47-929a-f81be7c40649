<?php

namespace models\packages\PackageBorrowerDisclosure;
use models\inArrayData;
use models\types\strongType;

class PackageBorrowerDisclosureData extends strongType
{
    public ?string $TodaysDate = null;  
    public ?string $ImageUrl = null;
    public ?string $propertyAddress = null;
    public ?string $borrowerName = null;
    public ?string $coBorrowerName = null;
    public ?string $isCoBorrower = null;
    public ?string $subjPropaddress = null;
    public ?string $circle = null;
    public ?string $circle_black = null;

    public function Init(
        array $inArray,
        int   $LMRId
    )
    {
        $inArrayData = inArrayData::fromInArray($inArray);

        //Get variables from DB/Array
        $fileDetails = $inArrayData->fileDetails[$LMRId];
        $LMRInfoArray = $fileDetails->LMRInfo;
        $filePropInfo = $fileDetails->FilePropInfo;

        $this->TodaysDate = date('m/d/Y');
        $circle = '<img alt="" class="circle" src="' . CONST_ASSETS_URL . 'images/imgs/circle.png">';
        $this->circle = $circle;
        $circle_black = '<img alt="" class="circle" src="' . CONST_ASSETS_URL . 'images/imgs/circle_black.png">';
        $this->circle_black = $circle_black;

        $this->borrowerName = ucfirst($LMRInfoArray->borrowerFName) . ' ' . ucfirst($LMRInfoArray->borrowerLName);
        $this->isCoBorrower = trim($LMRInfoArray->isCoBorrower);
        $this->coBorrowerName = trim($LMRInfoArray->isCoBorrower) ? ucfirst($LMRInfoArray->coBorrowerFName) . ' ' . ucfirst($LMRInfoArray->coBorrowerLName) : '';
        
        $subjPropaddress = $LMRInfoArray->propertyAddress;
        if ($filePropInfo->propertyUnit) {
            $subjPropaddress .= ', ' . $filePropInfo->propertyUnit;
        }
        $subjPropaddress .= ', ' . $LMRInfoArray->propertyCity . ', ' . $LMRInfoArray->propertyState . ' ' .
            $LMRInfoArray->propertyZip . ", USA";
        $this->subjPropaddress = $subjPropaddress;

        $this->checkMissing();
    }
}