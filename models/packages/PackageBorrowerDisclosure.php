<?php

namespace models\packages;

use models\packages\PackageBorrowerDisclosure\PackageBorrowerDisclosureData;
use models\pdf\CustomTCPDF;
use models\standard\Esign;
use models\standard\Strings;
use models\standard\Currency;
use models\standard\Dates;

class PackageBorrowerDisclosure extends CustomTCPDF
{
    public static ?PackageBorrowerDisclosureData $ReportData = null;

    public static function GeneratePDF(array $inArray, CustomTCPDF $pdf = null)
    {
        //==========start of mandatory=================================
        global $txnID, $glPositionArray, $topMarginVal, $leftAndRightMarginVal, $siteUrl, $rootPath, $assetsUrl;

        $glPositionArray = $glPositionArray ?? [];

        /** Page layout params **/
        $xVal = 15;
        $yVal = 15;

        $txnID = '';
        if (count($inArray) > 0) {
            if (array_key_exists('txnID', $inArray)) $txnID = trim($inArray['txnID']);
        }

        $LMRId = intval($inArray['LMRId'] ?? 0);

        if(!$LMRId) {
            //dd('$LMRId not set', debug_backtrace());
        }

        self::$ReportData = new PackageBorrowerDisclosureData();
        self::$ReportData->Init(
            $inArray,
            $LMRId
        );

        //=======END OF MANDATORY===========================================

        $pdf = $pdf ?: new self(
            PDF_PAGE_ORIENTATION,
            PDF_UNIT,
            PDF_PAGE_FORMAT,
            true,
            'UTF-8',
            false
        );

        //$pdf->setMargins($pdf->leftAndRightMarginVal, 0, $pdf->leftAndRightMarginVal);
        $pdf->setAutoPageBreak(TRUE, 15);

        $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
        $pdf->setFontSubsetting(false);

        $pdf->setLeftMargin($xVal);
        $pdf->setRightMargin($xVal);
        $pdf->setTopMargin($yVal);
        $pdf->setFont('helvetica', '', 7);
        $pdf->setCellHeightRatio('1.0');

        $pdf->AddPage('P','LETTER');
        $html = $pdf->generateHTML(self::$ReportData, __DIR__ . '/PackageBorrowerDisclosure/html/BorrowerDisclosure1.html');
        $pdf->writeHTML($html, true, 0, true, 0);

        Esign::Esign(
            5,
            -32,
            105,
            -32,
            self::$ReportData->isCoBorrower,
            0,
            $pdf,
            $glPositionArray
        );

        return $pdf;
    }

    public function Header()
    {
        
    }

    public function Footer()
    {
        global $txnID, $glPositionArray;

        $tbl = <<<EOD
        <table width="100%">
            <tr><td align="center" style="font-size:9.5pt;">51-BrokerDisclBase-20150811lh</td></tr>
        </table>
EOD;
        $this->SetXY(15, -12);
        $this->writeHTML($tbl, true, 0, true, 0);

        if (trim($txnID) != '') {
            $txnArray['txnID'] = $txnID;
            $this->addTxnNumberToFooterNew($txnArray);
        }
    }
}
