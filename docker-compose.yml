services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lendingwise_app
    volumes:
      # Mount the application code
      - ./:/var/www/html
    ports:
      - "80:80"
    environment:
      TZ: America/New_York
      # Database connection details
      DB_HOST: ${DB_HOST:-lendingwise_db}
      DB_NAME: ${DB_NAME:-lendingwise_local}
      DB_USER: ${DB_USER:-local_user}
      DB_PASS: ${DB_PASS:-local_pwd}
      DB_TYPE: ${DB_TYPE:-MySQL}
    depends_on:
      db:
        condition: service_healthy
    networks:
      - lendingwise_network
    command: >
      sh -c "apache2-foreground"

  db:
    image: mysql:8.0
    container_name: lendingwise_db
    environment:
      MYSQL_DATABASE: lendingwise_local
      MYSQL_USER: local_user
      MYSQL_PASSWORD: local_pwd
      # MySQL root and user credentials
      MYSQL_ROOT_PASSWORD: super12345
      TZ: UTC
    volumes:
      # Persist database data
      - mysql_data:/var/lib/mysql
      # Initialize database
      - ./db-init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command:
      --default-authentication-plugin=mysql_native_password
      --default-time-zone='America/New_York'
      --explicit-defaults-for-timestamp=ON
      --host-cache-size=0
      --innodb-buffer-pool-size=512M
      --log-bin-trust-function-creators=1
      --max-allowed-packet=256M
      --pid-file=/tmp/mysql.pid
      --sql-mode=""
      --tls-version=TLSv1.2,TLSv1.3
    healthcheck:
      test: ["CMD-SHELL", "mysqladmin ping -h localhost -u root -p'super12345' || exit 1"]
      interval: 20s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - lendingwise_network

volumes:
  mysql_data:

networks:
  lendingwise_network:
    driver: bridge

