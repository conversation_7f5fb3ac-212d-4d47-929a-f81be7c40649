{"devDependencies": {"@faker-js/faker": "^9.6.0", "cypress": "^14.5.3", "cypress-downloadfile": "^1.2.4", "cypress-file-upload": "^5.0.8", "dotenv": "^16.4.7", "fs-extra": "^11.3.0", "node-fetch": "^2.7.0", "pdf-parse": "^1.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@playwright/test": "^1.54.2", "cypress-multi-reporters": "^2.0.5", "cypress-parallel": "^0.15.0", "playwright": "^1.54.2"}, "scripts": {"cy:open-e2e": "cypress open --e2e --browser chrome", "cy:run-e2e": "cypress run --e2e --browser chrome --record", "cy:run-unit": "cypress run --component --browser chrome --record", "cy:parallel": "cypress-parallel -s cy:run -t 2 -d ./cypress/e2e"}}